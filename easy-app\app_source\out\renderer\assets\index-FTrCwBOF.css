/* Reset básico */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* CSS Variables for Themes - Default Light Theme */
:root {
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Fix for dropdown menu background */
[data-radix-popper-content-wrapper] {
  z-index: 50 !important;
}

/* Ensure dropdown menu has proper background */
.dropdown-menu-content {
  background: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Force theme application */
body {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Force background on main containers */
.bg-background {
  background-color: hsl(var(--background)) !important;
}

.text-foreground {
  color: hsl(var(--foreground)) !important;
}

.bg-card {
  background-color: hsl(var(--card)) !important;
}

.text-card-foreground {
  color: hsl(var(--card-foreground)) !important;
}

/* Sidebar theme application */
.sidebar {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Card components */
.card {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Button variants */
.btn-outline {
  border-color: hsl(var(--border)) !important;
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

.btn-outline:hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* Text colors */
.text-muted-foreground {
  color: hsl(var(--muted-foreground)) !important;
}

/* Border colors */
.border {
  border-color: hsl(var(--border)) !important;
}

/* Yellow buttons - always visible */
.bg-yellow-500 {
  background-color: #eab308 !important;
  color: #000000 !important;
}

.bg-yellow-500:hover {
  background-color: #ca8a04 !important;
  color: #000000 !important;
}

.border-yellow-500 {
  border-color: #eab308 !important;
}

/* Additional dropdown menu fixes */
[data-radix-dropdown-menu-content] {
  background: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
  color: hsl(var(--popover-foreground)) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

[data-radix-dropdown-menu-item] {
  color: hsl(var(--popover-foreground)) !important;
}

[data-radix-dropdown-menu-item]:hover {
  background: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}
