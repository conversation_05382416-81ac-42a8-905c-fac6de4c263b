"use strict";
const electron = require("electron");
const preload = require("@electron-toolkit/preload");
const api = {
  ping: () => electron.ipcRenderer.invoke("ping"),
  selectDirectory: () => electron.ipcRenderer.invoke("select-directory"),
  selectFile: (filters) => electron.ipcRenderer.invoke("select-file", filters),
  // Settings API
  getSettings: () => electron.ipcRenderer.invoke("ipc-settings-get"),
  updateSettings: (settings) => electron.ipcRenderer.invoke("ipc-settings-update", settings),
  // File operations
  openFile: (path) => electron.ipcRenderer.invoke("open-file", path),
  saveFile: (path, content) => electron.ipcRenderer.invoke("save-file", path, content),
  // Clouds API
  cloudsGetAll: () => electron.ipcRenderer.invoke("clouds-get-all"),
  cloudsCreate: (cloudData) => electron.ipcRenderer.invoke("clouds-create", cloudData),
  cloudsUpdate: (id, cloudData) => electron.ipcRenderer.invoke("clouds-update", id, cloudData),
  cloudsDelete: (id) => electron.ipcRenderer.invoke("clouds-delete", id),
  cloudsGetDetails: (cloudId) => electron.ipcRenderer.invoke("clouds-get-details", cloudId),
  cloudsReprocessCsv: (cloudId) => electron.ipcRenderer.invoke("clouds-reprocess-csv", cloudId),
  debugGetSchema: () => electron.ipcRenderer.invoke("debug-get-schema"),
  // Cloud download and processing APIs
  cloudStartDownload: (cloudId) => electron.ipcRenderer.invoke("cloud-start-download", cloudId),
  cloudProcessWithIped: (cloudId) => electron.ipcRenderer.invoke("cloud-process-with-iped", cloudId),
  cloudResolveFailedDownloads: (cloudId) => electron.ipcRenderer.invoke("cloud-resolve-failed-downloads", cloudId),
  cloudSetTargetName: (cloudId, targetName, dsId) => electron.ipcRenderer.invoke("cloud-set-target-name", cloudId, targetName, dsId),
  cloudCancelDownload: (cloudId) => electron.ipcRenderer.invoke("cloud-cancel-download", cloudId),
  cloudGetDownloadStatus: (cloudId) => electron.ipcRenderer.invoke("cloud-get-download-status", cloudId),
  // Targets API
  targetsGetAll: () => electron.ipcRenderer.invoke("targets-get-all"),
  targetsCreateOrUpdate: (targetData) => electron.ipcRenderer.invoke("targets-create-or-update", targetData),
  targetsDelete: (dsId) => electron.ipcRenderer.invoke("targets-delete", dsId),
  // Database API
  databaseGetStats: () => electron.ipcRenderer.invoke("database-get-stats"),
  databaseGetRecentActivity: () => electron.ipcRenderer.invoke("database-get-recent-activity"),
  databaseUpdateStats: () => electron.ipcRenderer.invoke("database-update-stats"),
  databaseCleanOldData: () => electron.ipcRenderer.invoke("database-clean-old-data"),
  databaseExportData: () => electron.ipcRenderer.invoke("database-export-data"),
  databaseImportData: () => electron.ipcRenderer.invoke("database-import-data"),
  databaseMaintenance: () => electron.ipcRenderer.invoke("database-maintenance"),
  // Event listeners for download progress
  onDownloadProgress: (callback) => {
    electron.ipcRenderer.on("download-progress", (_, data) => callback(data));
  },
  onDownloadCompleted: (callback) => {
    electron.ipcRenderer.on("download-completed", (_, data) => callback(data));
  },
  removeDownloadListeners: () => {
    electron.ipcRenderer.removeAllListeners("download-progress");
    electron.ipcRenderer.removeAllListeners("download-completed");
  },
  // Event listeners for CSV processing logs
  onCsvProcessingLog: (callback) => {
    electron.ipcRenderer.removeAllListeners("csv-processing-log");
    electron.ipcRenderer.on("csv-processing-log", (_, logMessage) => callback(logMessage));
  },
  removeCsvProcessingLogListener: () => {
    electron.ipcRenderer.removeAllListeners("csv-processing-log");
  },
  // Platform info
  platform: process.platform
};
if (process.contextIsolated) {
  try {
    electron.contextBridge.exposeInMainWorld("electron", preload.electronAPI);
    electron.contextBridge.exposeInMainWorld("api", api);
  } catch (error) {
    console.error(error);
  }
} else {
  window.electron = preload.electronAPI;
  window.api = api;
}
