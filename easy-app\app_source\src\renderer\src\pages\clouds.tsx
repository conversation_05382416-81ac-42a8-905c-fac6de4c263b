import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/renderer/src/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/renderer/src/components/ui/card'
import { Input } from '@/renderer/src/components/ui/input'
import { Label } from '@/renderer/src/components/ui/label'
import { useToast } from '@/renderer/src/hooks/use-toast'

interface Cloud {
  id: number
  name: string
  password: string
  status?: string
  target_name?: string
  createdAt: Date
  updatedAt: Date
  accountsCount: number
  linksCount: number
}

interface NewCloud {
  name: string
  password: string
  csvFile: string
  outputDir: string
}

export function Clouds() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const [clouds, setClouds] = useState<Cloud[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingCloud, setEditingCloud] = useState<Cloud | null>(null)
  const [newCloud, setNewCloud] = useState<NewCloud>({
    name: '',
    password: '',
    csvFile: '',
    outputDir: ''
  })
  const [logs, setLogs] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingCompleted, setProcessingCompleted] = useState(false)
  const logContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadClouds()

    // Listen for CSV processing logs
    const handleCsvLog = (logMessage: string) => {
      setLogs(prev => [...prev, logMessage])
    }

    window.api.onCsvProcessingLog(handleCsvLog)

    return () => {
      window.api.removeCsvProcessingLogListener()
    }
  }, [])

  // Auto-scroll to bottom when new logs are added
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
    }
  }, [logs])

  const loadClouds = async () => {
    setIsLoading(true)
    try {
      const cloudsData = await window.api.cloudsGetAll()
      setClouds(cloudsData)
    } catch (error) {
      console.error('Erro ao carregar nuvens:', error)
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Erro ao carregar nuvens do banco de dados"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString('pt-BR')
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
  }

  const handleCreateCloud = async () => {
    // Validar todos os campos obrigatórios
    if (!newCloud.name.trim()) {
      toast({
        variant: "destructive",
        title: "Campo obrigatório",
        description: "Nome da nuvem é obrigatório"
      })
      return
    }
    if (!newCloud.password.trim()) {
      toast({
        variant: "destructive",
        title: "Campo obrigatório",
        description: "Senha para descriptografar é obrigatória"
      })
      return
    }
    if (!newCloud.csvFile.trim()) {
      toast({
        variant: "destructive",
        title: "Campo obrigatório",
        description: "Arquivo CSV com os links é obrigatório"
      })
      return
    }
    if (!newCloud.outputDir.trim()) {
      toast({
        variant: "destructive",
        title: "Campo obrigatório",
        description: "Pasta para salvar os downloads é obrigatória"
      })
      return
    }

    setIsProcessing(true)
    setProcessingCompleted(false)
    setLogs([])

    try {
      await window.api.cloudsCreate({
        name: newCloud.name,
        password: newCloud.password,
        csvFile: newCloud.csvFile,
        outputDir: newCloud.outputDir
      })

      // Aguardar um pouco para mostrar os logs finais
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Recarregar a lista de nuvens
      await loadClouds()

      // Limpar apenas os campos do formulário, mas manter o formulário aberto
      setNewCloud({ name: '', password: '', csvFile: '', outputDir: '' })

      toast({
        variant: "success" as any,
        title: "Sucesso",
        description: "Nuvem criada com sucesso! Você pode ver os logs acima e fechar quando quiser."
      })

      setProcessingCompleted(true)
    } catch (error) {
      console.error('Erro ao criar nuvem:', error)
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Erro ao criar nuvem"
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleEditCloud = (cloud: Cloud) => {
    setEditingCloud(cloud)
    setNewCloud({
      name: cloud.name,
      password: cloud.password,
      csvFile: (cloud as any).csv_links || '',
      outputDir: (cloud as any).output_dir || ''
    })
    setShowCreateForm(true)
  }

  const handleUpdateCloud = async () => {
    // Validar todos os campos obrigatórios
    if (!editingCloud || !newCloud.name.trim()) {
      toast({
        variant: "destructive",
        title: "Campo obrigatório",
        description: "Nome da nuvem é obrigatório"
      })
      return
    }
    if (!newCloud.password.trim()) {
      toast({
        variant: "destructive",
        title: "Campo obrigatório",
        description: "Senha para descriptografar é obrigatória"
      })
      return
    }
    if (!newCloud.csvFile.trim()) {
      toast({
        variant: "destructive",
        title: "Campo obrigatório",
        description: "Arquivo CSV com os links é obrigatório"
      })
      return
    }
    if (!newCloud.outputDir.trim()) {
      toast({
        variant: "destructive",
        title: "Campo obrigatório",
        description: "Pasta para salvar os downloads é obrigatória"
      })
      return
    }

    try {
      await window.api.cloudsUpdate(editingCloud.id, {
        name: newCloud.name,
        password: newCloud.password,
        csvFile: newCloud.csvFile,
        outputDir: newCloud.outputDir
      })

      // Recarregar a lista de nuvens
      await loadClouds()

      setEditingCloud(null)
      setNewCloud({ name: '', password: '', csvFile: '', outputDir: '' })
      setShowCreateForm(false)

      toast({
        variant: "success" as any,
        title: "Sucesso",
        description: "Nuvem atualizada com sucesso!"
      })
    } catch (error) {
      console.error('Erro ao atualizar nuvem:', error)
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Erro ao atualizar nuvem"
      })
    }
  }

  const handleDeleteCloud = async (cloudId: number) => {
    if (!confirm('Tem certeza que deseja excluir esta nuvem? Esta ação não pode ser desfeita.')) {
      return
    }

    try {
      await window.api.cloudsDelete(cloudId)

      // Recarregar a lista de nuvens
      await loadClouds()

      toast({
        variant: "success" as any,
        title: "Sucesso",
        description: "Nuvem excluída com sucesso!"
      })
    } catch (error) {
      console.error('Erro ao excluir nuvem:', error)
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Erro ao excluir nuvem"
      })
    }
  }

  const cancelEdit = () => {
    setEditingCloud(null)
    setNewCloud({ name: '', password: '', csvFile: '', outputDir: '' })
    setProcessingCompleted(false)
    setLogs([])
    setShowCreateForm(false)
  }

  const handleCloudClick = (cloudId: number) => {
    navigate(`/clouds/${cloudId}`)
  }

  const handleSelectCsvFile = async () => {
    try {
      const file = await window.api.selectFile([
        { name: 'CSV Files', extensions: ['csv'] },
        { name: 'All Files', extensions: ['*'] }
      ])
      if (file) {
        setNewCloud(prev => ({ ...prev, csvFile: file }))
      }
    } catch (error) {
      console.error('Erro ao selecionar arquivo CSV:', error)
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Erro ao selecionar arquivo CSV"
      })
    }
  }

  const handleSelectOutputDir = async () => {
    try {
      const directory = await window.api.selectDirectory()
      if (directory) {
        setNewCloud(prev => ({ ...prev, outputDir: directory }))
      }
    } catch (error) {
      console.error('Erro ao selecionar pasta:', error)
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Erro ao selecionar pasta"
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Lista de nuvens</h1>
          <p className="text-muted-foreground">
            Gerencie seus projetos de extração de dados da nuvem
          </p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center space-x-2"
        >
          <span>➕</span>
          <span>Criar nuvem</span>
        </Button>
      </div>

      {/* Formulário de Criação/Edição */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingCloud ? 'Editar nuvem' : 'Criar nova nuvem'}
            </CardTitle>
            <CardDescription>
              {editingCloud
                ? 'Modifique as informações da nuvem selecionada'
                : 'Preencha as informações para criar uma nova nuvem'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nome da nuvem</Label>
                <Input
                  id="name"
                  value={newCloud.name}
                  onChange={(e) => setNewCloud(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Digite o nome da nuvem"
                  disabled={isProcessing}
                  autoFocus={!editingCloud}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Senha para descriptografar</Label>
                <Input
                  id="password"
                  type="password"
                  value={newCloud.password}
                  onChange={(e) => setNewCloud(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Digite a senha"
                  disabled={isProcessing}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="csvFile">Selecione o arquivo .csv com os links</Label>
                <div className="flex gap-2">
                  <Input
                    id="csvFile"
                    value={newCloud.csvFile}
                    readOnly
                    placeholder="click aqui para selecionar"
                    className="flex-1 cursor-pointer"
                    onClick={handleSelectCsvFile}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleSelectCsvFile}
                  >
                    Selecionar
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="outputDir">Selecione a pasta para salvar os downloads</Label>
                <div className="flex gap-2">
                  <Input
                    id="outputDir"
                    value={newCloud.outputDir}
                    readOnly
                    placeholder="click aqui para selecionar"
                    className="flex-1 cursor-pointer"
                    onClick={handleSelectOutputDir}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleSelectOutputDir}
                  >
                    Selecionar
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex space-x-2">
              {processingCompleted ? (
                <>
                  <Button
                    onClick={() => {
                      setProcessingCompleted(false)
                      setLogs([])
                      setNewCloud({ name: '', password: '', csvFile: '', outputDir: '' })
                    }}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow-lg"
                    size="lg"
                  >
                    ✨ Criar Nova Nuvem
                  </Button>
                  <Button
                    variant="outline"
                    onClick={cancelEdit}
                    className="flex-1 font-semibold shadow-lg"
                    size="lg"
                  >
                    📋 Voltar à Lista
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    onClick={editingCloud ? handleUpdateCloud : handleCreateCloud}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold shadow-lg"
                    size="lg"
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processando...' : 'Salvar'}
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={cancelEdit}
                    className="flex-1 font-semibold shadow-lg"
                    size="lg"
                    disabled={isProcessing}
                  >
                    Cancelar
                  </Button>
                </>
              )}
            </div>

            {/* Mensagem de sucesso */}
            {processingCompleted && (
              <div className="p-4 bg-green-50 border-2 border-green-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">🎉</span>
                  <div>
                    <h3 className="text-lg font-semibold text-green-800">Processamento Concluído!</h3>
                    <p className="text-sm text-green-700">
                      A nuvem foi criada com sucesso. Você pode ver todos os logs abaixo e decidir o que fazer a seguir.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Seção de logs */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Logs do Processamento</Label>
                {logs.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setLogs([])}
                    className="text-xs h-6 px-2"
                  >
                    🗑️ Limpar
                  </Button>
                )}
              </div>
              <div
                ref={logContainerRef}
                className="min-h-[150px] max-h-[250px] p-4 border-2 rounded-lg bg-gray-900 text-green-400 text-sm font-mono overflow-y-auto shadow-inner"
                style={{ scrollBehavior: 'smooth' }}
              >
                {logs.length === 0 ? (
                  <div className="text-gray-500 italic flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="text-2xl mb-2">💬</div>
                      <div>Logs aparecerão aqui durante o processamento...</div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {logs.map((log, index) => (
                      <div key={index} className="flex items-start space-x-2 animate-in slide-in-from-left-2 duration-200">
                        <span className="text-green-500 text-xs mt-0.5 flex-shrink-0">▶</span>
                        <span className="flex-1 leading-relaxed break-words">{log}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              {logs.length > 0 && (
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{logs.length} mensagem{logs.length !== 1 ? 's' : ''} de log</span>
                  <span className="text-green-500">● Rolagem automática ativa</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Lista de Nuvens */}
      <div className="space-y-4">
        {clouds.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-muted-foreground">
                <span className="text-4xl block mb-4">☁️</span>
                <p className="text-lg font-medium">Nenhuma nuvem encontrada</p>
                <p className="text-sm">Crie sua primeira nuvem para começar</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          clouds.map((cloud) => (
            <Card key={cloud.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1 cursor-pointer" onClick={() => handleCloudClick(cloud.id)}>
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="text-2xl">☁️</span>
                      <div>
                        <h3 className="text-lg font-semibold">{cloud.name}</h3>
                        {cloud.status && (
                          <p className="text-sm text-muted-foreground">Status: {cloud.status}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                      <span className="flex items-center space-x-1">
                        <span>👤</span>
                        <span>{cloud.accountsCount} contas</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <span>🔗</span>
                        <span>{cloud.linksCount} links</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <span>📅</span>
                        <span>Criado em {cloud.createdAt.toLocaleDateString('pt-BR')}</span>
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e: React.MouseEvent) => {
                        e.stopPropagation()
                        handleEditCloud(cloud)
                      }}
                      className="flex items-center space-x-1"
                    >
                      <span>✏️</span>
                      <span>Editar</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e: React.MouseEvent) => {
                        e.stopPropagation()
                        handleDeleteCloud(cloud.id)
                      }}
                      className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                    >
                      <span>🗑️</span>
                      <span>Excluir</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
