"use strict";
const electron = require("electron");
const node_path = require("node:path");
const utils = require("@electron-toolkit/utils");
const sqlite = require("sqlite");
const sqlite3 = require("sqlite3");
const child_process = require("child_process");
const fs = require("fs");
const path = require("path");
const os = require("os");
const AdmZip = require("adm-zip");
const XLSX = require("xlsx");
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const fs__namespace = /* @__PURE__ */ _interopNamespaceDefault(fs);
const path__namespace = /* @__PURE__ */ _interopNamespaceDefault(path);
const os__namespace = /* @__PURE__ */ _interopNamespaceDefault(os);
const XLSX__namespace = /* @__PURE__ */ _interopNamespaceDefault(XLSX);
const icon = node_path.join(__dirname, "../../resources/icon.png");
let db = null;
const activeDownloads = /* @__PURE__ */ new Map();
let mainWindow = null;
async function initDatabase() {
  try {
    const { app: app2 } = require("electron");
    const userDataPath = app2.getPath("userData");
    let dbPath;
    if (utils.is.dev) {
      dbPath = node_path.join(__dirname, "../../helper.db");
    } else {
      dbPath = node_path.join(userDataPath, "helper.db");
      const fs2 = require("fs");
      if (!fs2.existsSync(dbPath)) {
        const resourceDbPath = node_path.join(__dirname, "../../resources/helper.db");
        if (fs2.existsSync(resourceDbPath)) {
          fs2.copyFileSync(resourceDbPath, dbPath);
          console.log("Database copied to userData directory from app resources");
        } else {
          console.warn("Database not found in app resources, creating new one");
        }
      }
    }
    console.log("Database path:", dbPath);
    db = await sqlite.open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    console.log("Database connected successfully");
  } catch (error) {
    console.error("Failed to connect to database:", error);
  }
}
function createWindow() {
  mainWindow = new electron.BrowserWindow({
    width: 1080,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...process.platform === "linux" ? { icon } : {},
    webPreferences: {
      preload: node_path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: false
    }
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(node_path.join(__dirname, "../renderer/index.html"));
  }
}
electron.app.whenReady().then(async () => {
  utils.electronApp.setAppUserModelId("com.stratocumulus.app");
  electron.app.on("browser-window-created", (_, window) => {
    utils.optimizer.watchWindowShortcuts(window);
  });
  electron.ipcMain.handle("ping", () => "pong");
  await initDatabase();
  setTimeout(async () => {
    try {
      if (db) {
        await db.run(`
          CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            action TEXT NOT NULL,
            table_name TEXT NOT NULL,
            details TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);
        await db.run(`
          CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bash_path TEXT,
            iped_path TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);
        const existingSettings = await db.get("SELECT id FROM settings LIMIT 1");
        if (!existingSettings) {
          await db.run(`
            INSERT INTO settings (bash_path, iped_path)
            VALUES (?, ?)
          `, [null, null]);
          console.log("Default settings created");
        }
        await db.run(`
          INSERT INTO activity_log (action, table_name, details)
          VALUES (?, ?, ?)
        `, ["SYSTEM", "application", "Nimbus App iniciado com sucesso"]);
      }
    } catch (error) {
      console.error("Error during startup initialization:", error);
    }
  }, 1e3);
  electron.ipcMain.handle("clouds-get-all", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const clouds = await db.all(`
        SELECT
          c.*,
          (SELECT COUNT(*) FROM accounts WHERE cloud_id = c.id) as accountsCount,
          (SELECT COUNT(*) FROM work_queue WHERE cloud_id = c.id) as linksCount,
          (SELECT t.name FROM targets t
           JOIN accounts a ON t.ds_id = a.ds_id
           WHERE a.cloud_id = c.id LIMIT 1) as target_name
        FROM clouds c
        ORDER BY c.created_at DESC
      `);
      return clouds.map((cloud) => ({
        ...cloud,
        createdAt: new Date(cloud.created_at),
        updatedAt: new Date(cloud.updated_at || cloud.created_at)
      }));
    } catch (error) {
      console.error("Error fetching clouds:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("clouds-create", async (_, cloudData) => {
    try {
      if (!db) throw new Error("Database not connected");
      sendLog("Iniciando criação da nuvem...");
      sendLog(`Nome da nuvem: ${cloudData.name}`);
      sendLog(`Arquivo CSV: ${cloudData.csvFile}`);
      sendLog(`Pasta de destino: ${cloudData.outputDir}`);
      sendLog("Criando registro da nuvem no banco de dados...");
      const result = await db.run(`
        INSERT INTO clouds (name, password, csv_links, output_dir, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
      `, [cloudData.name, cloudData.password, cloudData.csvFile, cloudData.outputDir]);
      const cloudId = result.lastID;
      sendLog(`Nuvem criada com ID: ${cloudId}`);
      if (cloudData.csvFile && fs__namespace.existsSync(cloudData.csvFile)) {
        sendLog("Processando arquivo CSV...");
        await processCsvFile(cloudId, cloudData.csvFile, cloudData.outputDir, cloudData.password);
        sendLog("Processamento do arquivo CSV concluído!");
      } else {
        sendLog("Nenhum arquivo CSV válido fornecido");
      }
      sendLog("Criação da nuvem finalizada com sucesso!");
      return { id: cloudId, ...cloudData };
    } catch (error) {
      sendLog(`Erro ao criar nuvem: ${error}`);
      console.error("Error creating cloud:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("clouds-update", async (_, id, cloudData) => {
    try {
      if (!db) throw new Error("Database not connected");
      const currentCloud = await db.get("SELECT csv_links FROM clouds WHERE id = ?", [id]);
      await db.run(`
        UPDATE clouds
        SET name = ?, password = ?, csv_links = ?, output_dir = ?
        WHERE id = ?
      `, [cloudData.name, cloudData.password, cloudData.csvFile, cloudData.outputDir, id]);
      if (currentCloud && currentCloud.csv_links !== cloudData.csvFile) {
        if (cloudData.csvFile && fs__namespace.existsSync(cloudData.csvFile)) {
          await db.run("DELETE FROM accounts WHERE cloud_id = ?", [id]);
          await db.run("DELETE FROM work_queue WHERE cloud_id = ?", [id]);
          await processCsvFile(id, cloudData.csvFile, cloudData.outputDir, cloudData.password);
        }
      }
      return { id, ...cloudData };
    } catch (error) {
      console.error("Error updating cloud:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("clouds-delete", async (_, id) => {
    try {
      if (!db) throw new Error("Database not connected");
      await db.run("DELETE FROM accounts WHERE cloud_id = ?", [id]);
      await db.run("DELETE FROM work_queue WHERE cloud_id = ?", [id]);
      await db.run("DELETE FROM clouds WHERE id = ?", [id]);
      return { success: true };
    } catch (error) {
      console.error("Error deleting cloud:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("clouds-reprocess-csv", async (_, cloudId) => {
    try {
      if (!db) throw new Error("Database not connected");
      const cloud = await db.get("SELECT csv_links FROM clouds WHERE id = ?", [cloudId]);
      if (!cloud) {
        return { success: false, message: "Nuvem não encontrada" };
      }
      if (!cloud.csv_links || !fs__namespace.existsSync(cloud.csv_links)) {
        return { success: false, message: "Arquivo CSV não encontrado" };
      }
      await db.run("DELETE FROM accounts WHERE cloud_id = ?", [cloudId]);
      await db.run("DELETE FROM work_queue WHERE cloud_id = ?", [cloudId]);
      await processCsvFile(cloudId, cloud.csv_links, cloud.output_dir, cloud.password);
      return { success: true, message: "CSV reprocessado com sucesso" };
    } catch (error) {
      console.error("Error reprocessing CSV:", error);
      return { success: false, message: `Erro ao reprocessar CSV: ${error.message}` };
    }
  });
  electron.ipcMain.handle("clouds-get-details", async (_, cloudId) => {
    try {
      if (!db) throw new Error("Database not connected");
      const cloud = await db.get(`
        SELECT c.*,
               (SELECT t.name FROM targets t
                JOIN accounts a ON t.ds_id = a.ds_id
                WHERE a.cloud_id = c.id LIMIT 1) as target_name
        FROM clouds c
        WHERE c.id = ?
      `, [cloudId]);
      if (!cloud) throw new Error("Cloud not found");
      const accounts = await db.all(`
        SELECT a.*, t.name as alvo
        FROM accounts a
        LEFT JOIN targets t ON a.ds_id = t.ds_id
        WHERE a.cloud_id = ?
        ORDER BY a.id DESC
      `, [cloudId]);
      const links = await db.all(`
        SELECT * FROM work_queue
        WHERE cloud_id = ?
        ORDER BY id DESC
      `, [cloudId]);
      return {
        cloud: {
          ...cloud,
          createdAt: cloud.created_at ? new Date(cloud.created_at) : /* @__PURE__ */ new Date(),
          updatedAt: cloud.updated_at ? new Date(cloud.updated_at || cloud.created_at) : /* @__PURE__ */ new Date()
        },
        accounts: accounts.map((account) => ({
          ...account,
          createdAt: account.created_at ? new Date(account.created_at) : /* @__PURE__ */ new Date(),
          updatedAt: account.updated_at ? new Date(account.updated_at) : /* @__PURE__ */ new Date()
        })),
        links: links.map((link) => ({
          ...link,
          createdAt: link.created_at ? new Date(link.created_at) : /* @__PURE__ */ new Date(),
          updatedAt: link.updated_at ? new Date(link.updated_at) : /* @__PURE__ */ new Date()
        }))
      };
    } catch (error) {
      console.error("Error fetching cloud details:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("debug-get-schema", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const tables = await db.all(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `);
      const schema = {};
      for (const table of tables) {
        const columns = await db.all(`PRAGMA table_info(${table.name})`);
        schema[table.name] = columns;
      }
      return schema;
    } catch (error) {
      console.error("Error getting schema:", error);
      throw error;
    }
  });
  function sendLog(message) {
    const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString("pt-BR");
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    electron.BrowserWindow.getAllWindows().forEach((window) => {
      window.webContents.send("csv-processing-log", logMessage);
    });
  }
  async function processCsvFile(cloudId, csvFilePath, outputDir, password) {
    try {
      sendLog(`Iniciando processamento do arquivo CSV`);
      sendLog(`Arquivo: ${csvFilePath}`);
      const csvContent = fs__namespace.readFileSync(csvFilePath, "utf-8");
      const lines = csvContent.split("\n").filter((line) => line.trim());
      if (lines.length === 0) {
        sendLog("Arquivo CSV está vazio");
        return;
      }
      const header = lines[0].toLowerCase();
      sendLog(`Cabeçalho CSV: ${header}`);
      sendLog(`Total de linhas para processar: ${lines.length - 1}`);
      let accountsProcessed = 0;
      let linksProcessed = 0;
      let finalOutputDir = outputDir;
      if (!finalOutputDir) {
        const cloud = await db.get("SELECT output_dir FROM clouds WHERE id = ?", [cloudId]);
        finalOutputDir = cloud?.output_dir;
      }
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        try {
          sendLog(`Processando linha ${i}/${lines.length - 1}`);
          const fields = parseCsvLine(line);
          const isAccount = isAccountData(fields, header);
          const isLink = isLinkData(fields, header);
          if (isAccount) {
            await processAccountData(cloudId, fields, header);
            accountsProcessed++;
            const emailField = fields.find((field) => field.includes("@"));
            const email = emailField ? emailField.trim() : "N/A";
            sendLog(`Conta adicionada: ${email} (${accountsProcessed} total)`);
          } else if (isLink) {
            await processLinkData(cloudId, fields, header, finalOutputDir, password);
            linksProcessed++;
            sendLog(`Link adicionado (${linksProcessed} total)`);
          } else {
            sendLog(`Linha ${i} ignorada - formato não reconhecido`);
          }
        } catch (error) {
          sendLog(`Erro na linha ${i}: ${error}`);
        }
      }
      const totalAccounts = await db.get("SELECT COUNT(*) as count FROM accounts WHERE cloud_id = ?", [cloudId]);
      const accountsFromLinks = totalAccounts?.count || 0;
      sendLog(`=== Processamento CSV concluído ===`);
      sendLog(`Contas extraídas dos links: ${accountsFromLinks}`);
      sendLog(`Contas processadas diretamente: ${accountsProcessed}`);
      sendLog(`Links processados: ${linksProcessed}`);
      sendLog(`Total de linhas processadas: ${lines.length - 1}`);
      if (finalOutputDir && linksProcessed > 0) {
        await createAccountLinksCSV(cloudId, finalOutputDir);
      }
    } catch (error) {
      sendLog(`Erro ao processar arquivo CSV: ${error}`);
      throw error;
    }
  }
  function parseCsvLine(line) {
    const fields = [];
    let current = "";
    let inQuotes = false;
    let i = 0;
    while (i < line.length) {
      const char = line[i];
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"';
          i += 2;
        } else {
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === "," && !inQuotes) {
        fields.push(current.trim());
        current = "";
        i++;
      } else {
        current += char;
        i++;
      }
    }
    fields.push(current.trim());
    return fields;
  }
  function isAccountData(fields, header) {
    const hasEmail = fields.some((field) => field.includes("@"));
    const hasDsid = fields.some((field) => /^\d{10,}$/.test(field));
    const hasSize = fields.some((field) => /\d+(\.\d+)?\s*(GB|MB|KB)/i.test(field));
    return hasEmail && (hasDsid || hasSize);
  }
  function isLinkData(fields, header) {
    return fields.some(
      (field) => field.startsWith("http://") || field.startsWith("https://") || field.includes("icloud.com") || field.includes("apple.com")
    );
  }
  async function processAccountData(cloudId, fields, header) {
    try {
      let dsId = "";
      let email = "";
      let cloudSize = "";
      let expiryDate = "";
      let alvo = "";
      console.log(`Processing account data - Fields: ${JSON.stringify(fields)}`);
      console.log(`Header: ${header}`);
      const emailField = fields.find((field) => field.includes("@"));
      if (emailField) email = emailField.trim();
      const dsidField = fields.find((field) => /^\d{10,}$/.test(field.trim()));
      if (dsidField) dsId = dsidField.trim();
      const sizeField = fields.find((field) => /\d+(\.\d+)?\s*(GB|MB|KB)/i.test(field));
      if (sizeField) cloudSize = sizeField.trim();
      const dateField = fields.find(
        (field) => /\d{2}[-\/]\d{2}[-\/]\d{4}/.test(field) || /\d{4}[-\/]\d{2}[-\/]\d{2}/.test(field) || /\d{2}[-\/]\d{2}[-\/]\d{2}/.test(field)
      );
      if (dateField) expiryDate = dateField.trim();
      const alvoField = fields.find((field) => {
        const trimmed = field.trim();
        return trimmed.length > 0 && !trimmed.includes("@") && // not email
        !/^\d+$/.test(trimmed) && // not pure number
        !/\d+(\.\d+)?\s*(GB|MB|KB)/i.test(trimmed) && // not size
        !/\d{2}[-\/]\d{2}[-\/]\d{4}/.test(trimmed) && // not date
        !/\d{4}[-\/]\d{2}[-\/]\d{2}/.test(trimmed) && // not date
        !/\d{2}[-\/]\d{2}[-\/]\d{2}/.test(trimmed) && // not date
        !trimmed.startsWith("http") && // not URL
        trimmed.length < 50;
      });
      if (alvoField) alvo = alvoField.trim();
      console.log(`Extracted data - DSID: ${dsId}, Email: ${email}, Size: ${cloudSize}, Expiry: ${expiryDate}, Alvo: ${alvo}`);
      if (email || dsId) {
        const existingAccount = await db.get(
          "SELECT id FROM accounts WHERE cloud_id = ? AND (ds_id = ? OR email = ?)",
          [cloudId, dsId, email]
        );
        if (!existingAccount) {
          await db.run(`
            INSERT INTO accounts (
              cloud_id, ds_id, email, cloud_size, expiry_date, alvo
            ) VALUES (?, ?, ?, ?, ?, ?)
          `, [cloudId, dsId, email, cloudSize, expiryDate, alvo]);
          console.log(`Added account: ${email} (${dsId}) - Alvo: ${alvo}`);
          if (alvo && dsId) {
            try {
              const existingTarget = await db.get("SELECT id FROM targets WHERE ds_id = ?", [dsId]);
              if (existingTarget) {
                await db.run("UPDATE targets SET name = ?, email = ? WHERE ds_id = ?", [alvo, email, dsId]);
                console.log(`Updated existing target for ${dsId}: ${alvo}`);
              } else {
                await db.run("INSERT INTO targets (ds_id, email, name) VALUES (?, ?, ?)", [dsId, email, alvo]);
                console.log(`Created new target for ${dsId}: ${alvo}`);
              }
            } catch (targetError) {
              console.error("Error creating/updating target:", targetError);
            }
          }
        } else {
          console.log(`Account already exists: ${email} (${dsId})`);
        }
      } else {
        console.warn(`Skipping line - no email or DSID found in fields: ${JSON.stringify(fields)}`);
      }
    } catch (error) {
      console.error("Error processing account data:", error);
    }
  }
  async function processLinkData(cloudId, fields, header, outputDir, password) {
    try {
      const urlField = fields.find(
        (field) => field.startsWith("http://") || field.startsWith("https://") || field.includes("icloud.com") || field.includes("apple.com")
      );
      if (!urlField) {
        return;
      }
      const fileLink = urlField;
      if (fileLink.includes("apple-account")) {
        sendLog(`Detectado link de conta: ${fileLink}`);
        if (password) {
          sendLog(`Processando link de conta para extrair informações reais...`);
          await extractAccountFromLink(cloudId, fileLink, password);
        } else {
          sendLog(`Senha não fornecida para descriptografar conta`);
        }
        const fileName = extractFilenameFromUrl(fileLink);
        const existingLink = await db.get(
          "SELECT id FROM work_queue WHERE cloud_id = ? AND url = ?",
          [cloudId, fileLink]
        );
        if (!existingLink) {
          await db.run(`
            INSERT INTO work_queue (
              cloud_id, file_name, url, status
            ) VALUES (?, ?, ?, 'pending')
          `, [cloudId, fileName, fileLink]);
          console.log(`Added account link: ${fileLink}`);
        }
      } else {
        sendLog(`Link ignorado (não é conta): ${fileLink.substring(0, 100)}...`);
      }
    } catch (error) {
      console.error("Error processing link data:", error);
    }
  }
  function extractFilenameFromUrl(url) {
    try {
      const urlParts = url.split("/");
      const lastPart = urlParts[urlParts.length - 1];
      return lastPart.split("?")[0];
    } catch (error) {
      return `account_${Date.now()}.zip.gpg`;
    }
  }
  async function checkGpgAvailability() {
    return new Promise((resolve) => {
      const gpgProcess = child_process.spawn("gpg", ["--version"], {
        stdio: ["pipe", "pipe", "pipe"]
      });
      gpgProcess.on("close", (code) => {
        resolve(code === 0);
      });
      gpgProcess.on("error", () => {
        resolve(false);
      });
    });
  }
  async function downloadAndExtractAccountInfo(accountLink, password) {
    try {
      sendLog(`📥 Baixando arquivo de conta...`);
      const gpgAvailable = await checkGpgAvailability();
      if (!gpgAvailable) {
        sendLog(`❌ GPG não está instalado ou não está disponível no PATH`);
        throw new Error("GPG not available");
      }
      sendLog(`✅ GPG está disponível`);
      const tempDir = path__namespace.join(os__namespace.tmpdir(), "nimbus_accounts", `account_${Date.now()}`);
      await fs__namespace.promises.mkdir(tempDir, { recursive: true });
      const urlParts = accountLink.split("/");
      const lastPart = urlParts[urlParts.length - 1];
      const encryptedFilename = lastPart.split("?")[0];
      const encryptedFilePath = path__namespace.join(tempDir, encryptedFilename);
      sendLog(`⬇️ Baixando: ${encryptedFilename}`);
      const response = await fetch(accountLink);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const buffer = await response.arrayBuffer();
      await fs__namespace.promises.writeFile(encryptedFilePath, Buffer.from(buffer));
      sendLog(`✅ Arquivo baixado: ${encryptedFilename}`);
      const decryptedFilename = encryptedFilename.replace(".gpg", "");
      const decryptedFilePath = path__namespace.join(tempDir, decryptedFilename);
      sendLog(`🔓 Descriptografando arquivo com senha da nuvem...`);
      await decryptFile(encryptedFilePath, decryptedFilePath, password);
      await fs__namespace.promises.unlink(encryptedFilePath);
      sendLog(`🗑️ Arquivo encriptado removido`);
      sendLog(`📦 Extraindo arquivo ZIP...`);
      const extractDir = path__namespace.join(tempDir, "extracted");
      await extractZipFile(decryptedFilePath, extractDir);
      await fs__namespace.promises.unlink(decryptedFilePath);
      sendLog(`🗑️ Arquivo ZIP removido`);
      const accountInfo = await findAccountInfoInExtractedFiles(extractDir);
      await fs__namespace.promises.rmdir(tempDir, { recursive: true });
      sendLog(`🧹 Diretório temporário limpo`);
      if (accountInfo.realDsid) {
        sendLog(`✅ Conta encontrada: ${accountInfo.accountName} (${accountInfo.email}) - DSID: ${accountInfo.realDsid}`);
      } else {
        sendLog(`⚠️ Informações parciais encontradas: ${accountInfo.accountName} (${accountInfo.email})`);
      }
      return accountInfo;
    } catch (error) {
      sendLog(`❌ Erro ao processar conta: ${error}`);
      throw error;
    }
  }
  async function decryptFile(encryptedPath, decryptedPath, password) {
    return new Promise((resolve, reject) => {
      sendLog(`Iniciando descriptografia GPG...`);
      sendLog(`Arquivo origem: ${encryptedPath}`);
      sendLog(`Arquivo destino: ${decryptedPath}`);
      sendLog(`Senha fornecida: ${password ? "[SENHA FORNECIDA]" : "[SENHA VAZIA]"}`);
      const gpgProcess = child_process.spawn("gpg", [
        "--batch",
        "--yes",
        "--pinentry-mode",
        "loopback",
        "--passphrase",
        password,
        "--decrypt",
        "--output",
        decryptedPath,
        encryptedPath
      ], {
        stdio: ["pipe", "pipe", "pipe"]
      });
      let stderr = "";
      let stdout = "";
      gpgProcess.stdout?.on("data", (data) => {
        stdout += data.toString();
      });
      gpgProcess.stderr?.on("data", (data) => {
        stderr += data.toString();
        sendLog(`GPG stderr: ${data.toString()}`);
      });
      gpgProcess.on("close", (code) => {
        sendLog(`GPG processo finalizado com código: ${code}`);
        if (stdout) sendLog(`GPG stdout: ${stdout}`);
        if (stderr) sendLog(`GPG stderr completo: ${stderr}`);
        if (code === 0) {
          sendLog(`Descriptografia GPG bem-sucedida!`);
          resolve();
        } else {
          sendLog(`Descriptografia GPG falhou com código ${code}`);
          reject(new Error(`GPG decryption failed with code ${code}. stderr: ${stderr}`));
        }
      });
      gpgProcess.on("error", (error) => {
        sendLog(`Erro no processo GPG: ${error.message}`);
        reject(new Error(`GPG process error: ${error.message}`));
      });
    });
  }
  async function extractZipFile(zipPath, extractDir) {
    try {
      const zip = new AdmZip(zipPath);
      zip.extractAllTo(extractDir, true);
    } catch (error) {
      throw new Error(`ZIP extraction failed: ${error.message}`);
    }
  }
  async function readAccountDetailsFromExcel(filePath) {
    try {
      sendLog(`Lendo planilha Excel: ${path__namespace.basename(filePath)}`);
      const workbook = XLSX__namespace.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX__namespace.utils.sheet_to_json(worksheet, { header: 1 });
      sendLog(`Planilha contém ${data.length} linhas`);
      let email = "";
      let dsid = "";
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (!row || row.length === 0) continue;
        const rowStr = row.join(" ").toLowerCase();
        if (rowStr.includes("apple id") || i >= 6 && i <= 10) {
          for (const cell of row) {
            const cellStr = String(cell || "").trim();
            if (cellStr.includes("@") && cellStr.includes(".") && !cellStr.toLowerCase().includes("apple")) {
              email = cellStr;
              sendLog(`Email encontrado na linha ${i + 1}: ${email}`);
              break;
            }
          }
        }
        if (rowStr.includes("ds id") || rowStr.includes("dsid") || i >= 6 && i <= 10) {
          for (const cell of row) {
            const cellStr = String(cell || "").trim();
            if (/^\d{10,}$/.test(cellStr)) {
              dsid = cellStr;
              sendLog(`DSID encontrado na linha ${i + 1}: ${dsid}`);
              break;
            }
          }
        }
        for (const cell of row) {
          const cellStr = String(cell || "").trim();
          if (!email && cellStr.includes("@") && cellStr.includes(".") && !cellStr.toLowerCase().includes("apple") && !cellStr.toLowerCase().includes("id")) {
            email = cellStr;
            sendLog(`Email encontrado: ${email}`);
          }
          if (!dsid && /^\d{10,}$/.test(cellStr)) {
            dsid = cellStr;
            sendLog(`DSID encontrado: ${dsid}`);
          }
        }
        if (email && dsid) {
          break;
        }
      }
      if (email && dsid) {
        sendLog(`Informações extraídas da planilha - Email: ${email}, DSID: ${dsid}`);
        return { email, dsid };
      }
      sendLog(`Não foi possível extrair informações completas da planilha (Email: ${email || "não encontrado"}, DSID: ${dsid || "não encontrado"})`);
      return null;
    } catch (error) {
      sendLog(`Erro ao ler planilha Excel: ${error}`);
      return null;
    }
  }
  async function findAccountInfoInExtractedFiles(extractDir) {
    try {
      sendLog(`🔍 Procurando informações de conta em: ${extractDir}`);
      const files = await fs__namespace.promises.readdir(extractDir, { withFileTypes: true });
      sendLog(`📁 Estrutura encontrada: ${files.map((f) => f.name).join(", ")}`);
      const accountInfo = await findAccountDetailsInDirectory(extractDir);
      if (accountInfo) {
        return {
          email: accountInfo.email,
          accountName: accountInfo.email.split("@")[0],
          realDsid: accountInfo.dsid
          // Return the real DSID from the Excel file
        };
      }
      for (const file of files) {
        if (file.isDirectory()) {
          const folderPath = path__namespace.join(extractDir, file.name);
          sendLog(`📂 Explorando diretório: ${file.name}`);
          if (file.name.includes("@")) {
            const parts = file.name.split("-");
            const email = parts[0];
            const accountId = parts[1];
            sendLog(`✅ Encontrado diretório com email: ${email}`);
            return {
              email,
              accountName: email.split("@")[0],
              // Just the username part
              realDsid: accountId
              // Use the ID from folder name as DSID
            };
          }
          try {
            const subFiles = await fs__namespace.promises.readdir(folderPath);
            sendLog(`📄 Arquivos em ${file.name}: ${subFiles.join(", ")}`);
            for (const subFile of subFiles) {
              const subFilePath = path__namespace.join(folderPath, subFile);
              const subFileStat = await fs__namespace.promises.stat(subFilePath);
              if (subFileStat.isDirectory()) {
                sendLog(`📂 Explorando subdiretório: ${subFile}`);
                try {
                  const accountFiles = await fs__namespace.promises.readdir(subFilePath);
                  sendLog(`📄 Arquivos na conta ${subFile}: ${accountFiles.join(", ")}`);
                  for (const accountFile of accountFiles) {
                    sendLog(`📄 Verificando item: ${accountFile}`);
                    if (accountFile.includes("@") && accountFile.includes("-")) {
                      const parts = accountFile.split("-");
                      const email = parts[0];
                      const accountId = parts[parts.length - 1];
                      sendLog(`✅ Email extraído do nome do arquivo/pasta: ${email} (ID: ${accountId})`);
                      sendLog(`🎯 RETORNANDO INFORMAÇÕES DA CONTA ENCONTRADA!`);
                      return {
                        email,
                        accountName: email.split("@")[0],
                        realDsid: accountId
                        // Use the ID from folder/file name as DSID
                      };
                    }
                    const accountFilePath = path__namespace.join(subFilePath, accountFile);
                    const accountFileStat = await fs__namespace.promises.stat(accountFilePath);
                    if (accountFileStat.isFile()) {
                      if (accountFile.toLowerCase().endsWith(".xlsx")) {
                        sendLog(`📊 Analisando arquivo Excel: ${accountFile}`);
                        const excelInfo = await readAccountDetailsFromExcel(accountFilePath);
                        if (excelInfo) {
                          sendLog(`✅ Informações extraídas do Excel: ${excelInfo.email} (DSID: ${excelInfo.dsid})`);
                          return {
                            email: excelInfo.email,
                            accountName: excelInfo.email.split("@")[0],
                            realDsid: excelInfo.dsid
                          };
                        }
                      } else if (accountFile.toLowerCase().includes("account") || accountFile.toLowerCase().includes("info")) {
                        sendLog(`📄 Analisando arquivo de informações: ${accountFile}`);
                        const accountInfo2 = await extractAccountInfoFromFile(accountFilePath);
                        if (accountInfo2) {
                          return {
                            email: accountInfo2.email,
                            accountName: accountInfo2.accountName,
                            realDsid: void 0
                            // No DSID from file content
                          };
                        }
                      }
                    } else if (accountFileStat.isDirectory()) {
                      sendLog(`📂 Encontrado subdiretório dentro da conta: ${accountFile}`);
                      try {
                        const subAccountFiles = await fs__namespace.promises.readdir(accountFilePath);
                        sendLog(`📄 Arquivos em ${accountFile}: ${subAccountFiles.join(", ")}`);
                        for (const subAccountFile of subAccountFiles) {
                          if (subAccountFile.toLowerCase().endsWith(".xlsx")) {
                            sendLog(`📊 Analisando Excel em subdiretório: ${subAccountFile}`);
                            const excelInfo = await readAccountDetailsFromExcel(path__namespace.join(accountFilePath, subAccountFile));
                            if (excelInfo) {
                              sendLog(`✅ Informações extraídas do Excel: ${excelInfo.email} (DSID: ${excelInfo.dsid})`);
                              return {
                                email: excelInfo.email,
                                accountName: excelInfo.email.split("@")[0],
                                realDsid: excelInfo.dsid
                              };
                            }
                          }
                        }
                      } catch (error) {
                        sendLog(`⚠️ Erro ao ler subdiretório ${accountFile}: ${error}`);
                      }
                    }
                  }
                } catch (error) {
                  sendLog(`⚠️ Erro ao ler subdiretório ${subFile}: ${error}`);
                }
              } else {
                if (subFile.toLowerCase().endsWith(".xlsx") && subFile.toLowerCase().includes("account")) {
                  sendLog(`📊 Analisando arquivo Excel no primeiro nível: ${subFile}`);
                  const excelInfo = await readAccountDetailsFromExcel(path__namespace.join(folderPath, subFile));
                  if (excelInfo) {
                    sendLog(`✅ Informações extraídas do Excel: ${excelInfo.email} (DSID: ${excelInfo.dsid})`);
                    return {
                      email: excelInfo.email,
                      accountName: excelInfo.email.split("@")[0],
                      realDsid: excelInfo.dsid
                    };
                  }
                }
              }
            }
          } catch (error) {
            sendLog(`⚠️ Erro ao ler diretório ${file.name}: ${error}`);
          }
        } else {
          if (file.name.toLowerCase().endsWith(".xlsx") && file.name.toLowerCase().includes("account")) {
            sendLog(`📊 Analisando arquivo Excel na raiz: ${file.name}`);
            const excelInfo = await readAccountDetailsFromExcel(path__namespace.join(extractDir, file.name));
            if (excelInfo) {
              sendLog(`✅ Informações extraídas do Excel: ${excelInfo.email} (DSID: ${excelInfo.dsid})`);
              return {
                email: excelInfo.email,
                accountName: excelInfo.email.split("@")[0],
                realDsid: excelInfo.dsid
              };
            }
          }
        }
      }
      throw new Error("Não foi possível encontrar informações de conta válidas nos arquivos extraídos");
    } catch (error) {
      throw new Error(`Failed to find account info: ${error.message}`);
    }
  }
  async function findAccountDetailsInDirectory(dirPath) {
    try {
      sendLog(`Procurando estrutura de diretórios em: ${dirPath}`);
      const accountsDir = path__namespace.join(dirPath, "accounts");
      if (!fs__namespace.existsSync(accountsDir)) {
        sendLog(`Diretório 'accounts' não encontrado em: ${dirPath}`);
        return null;
      }
      sendLog(`Diretório 'accounts' encontrado: ${accountsDir}`);
      const dsidDirs = await fs__namespace.promises.readdir(accountsDir, { withFileTypes: true });
      for (const dsidDir of dsidDirs) {
        if (!dsidDir.isDirectory()) continue;
        const dsidPath = path__namespace.join(accountsDir, dsidDir.name);
        const realDsid = dsidDir.name;
        sendLog(`Processando DSID: ${realDsid}`);
        try {
          const idDirs = await fs__namespace.promises.readdir(dsidPath, { withFileTypes: true });
          for (const idDir of idDirs) {
            if (!idDir.isDirectory()) continue;
            const idPath = path__namespace.join(dsidPath, idDir.name);
            sendLog(`Processando ID: ${idDir.name}`);
            try {
              const emailDirs = await fs__namespace.promises.readdir(idPath, { withFileTypes: true });
              for (const emailDir of emailDirs) {
                if (!emailDir.isDirectory()) continue;
                const emailPath = path__namespace.join(idPath, emailDir.name);
                sendLog(`Processando diretório: ${emailDir.name}`);
                let email = "";
                if (emailDir.name.includes("@") && emailDir.name.includes("-")) {
                  email = emailDir.name.split("-")[0];
                  sendLog(`Email extraído do diretório: ${email}`);
                }
                const accountPath = path__namespace.join(emailPath, "Account");
                if (fs__namespace.existsSync(accountPath)) {
                  sendLog(`Diretório 'Account' encontrado: ${accountPath}`);
                  try {
                    const accountFiles = await fs__namespace.promises.readdir(accountPath, { withFileTypes: true });
                    for (const file of accountFiles) {
                      if (file.isFile() && file.name.toLowerCase().includes("accountdetails") && file.name.toLowerCase().endsWith(".xlsx")) {
                        const filePath = path__namespace.join(accountPath, file.name);
                        sendLog(`Planilha AccountDetails encontrada: ${file.name}`);
                        const excelInfo = await readAccountDetailsFromExcel(filePath);
                        const finalEmail = email || (excelInfo ? excelInfo.email : "");
                        const finalDsid = realDsid || (excelInfo ? excelInfo.dsid : "");
                        if (finalEmail && finalDsid) {
                          sendLog(`Informações finais - Email: ${finalEmail}, DSID: ${finalDsid}`);
                          return { email: finalEmail, dsid: finalDsid };
                        }
                      }
                    }
                  } catch (error) {
                    sendLog(`Erro ao ler diretório Account: ${error}`);
                  }
                }
              }
            } catch (error) {
              sendLog(`Erro ao ler diretório ID ${idDir.name}: ${error}`);
            }
          }
        } catch (error) {
          sendLog(`Erro ao ler diretório DSID ${dsidDir.name}: ${error}`);
        }
      }
      sendLog(`Nenhuma planilha AccountDetails encontrada na estrutura de diretórios`);
      return null;
    } catch (error) {
      sendLog(`Erro ao procurar planilhas em ${dirPath}: ${error}`);
      return null;
    }
  }
  async function extractAccountInfoFromFile(filePath) {
    try {
      const content = await fs__namespace.promises.readFile(filePath, "utf8");
      const emailMatch = content.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (emailMatch) {
        const email = emailMatch[1];
        const accountName = email.split("@")[0];
        return { email, accountName };
      }
      return null;
    } catch (error) {
      return null;
    }
  }
  async function extractAccountFromLink(cloudId, accountLink, password) {
    try {
      sendLog(`Iniciando processamento do link de conta...`);
      sendLog(`Link: ${accountLink.substring(0, 100)}...`);
      const realDsidFromFilename = extractDsidFromFilename(accountLink);
      const accountInfo = await downloadAndExtractAccountInfo(accountLink, password);
      if (!accountInfo.email) {
        sendLog(`Não foi possível extrair email da conta`);
        return;
      }
      const finalDsid = realDsidFromFilename || accountInfo.realDsid;
      if (!finalDsid) {
        sendLog(`Não foi possível extrair DSID válido`);
        return;
      }
      const existingAccount = await db.get(
        "SELECT id FROM accounts WHERE cloud_id = ? AND ds_id = ?",
        [cloudId, finalDsid]
      );
      if (!existingAccount) {
        await db.run(`
          INSERT INTO accounts (
            cloud_id, ds_id, email, cloud_size, expiry_date, alvo
          ) VALUES (?, ?, ?, ?, ?, ?)
        `, [cloudId, finalDsid, accountInfo.email, "", "", accountInfo.accountName]);
        sendLog(`✅ Conta adicionada: ${accountInfo.email} (DSID: ${finalDsid}, Nome: ${accountInfo.accountName})`);
        console.log(`Added account from link: ${accountInfo.email} (${finalDsid})`);
      } else {
        sendLog(`Conta já existe: ${accountInfo.email} (DSID: ${finalDsid})`);
      }
    } catch (error) {
      sendLog(`❌ Erro ao extrair conta do link: ${error}`);
      console.error("Error extracting account from link:", error);
    }
  }
  async function createAccountLinksCSV(cloudId, outputDir) {
    try {
      sendLog(`Criando arquivo CSV com links de conta em: ${outputDir}`);
      const accountLinks = await db.all(
        'SELECT * FROM work_queue WHERE cloud_id = ? AND url LIKE "%apple-account%" ORDER BY file_name',
        [cloudId]
      );
      if (accountLinks.length === 0) {
        sendLog("Nenhum link de conta encontrado");
        return;
      }
      if (!fs__namespace.existsSync(outputDir)) {
        fs__namespace.mkdirSync(outputDir, { recursive: true });
      }
      const csvPath = path__namespace.join(outputDir, "account_links.csv");
      const csvContent = [
        "FileName,URL,Status",
        ...accountLinks.map((item) => `"${item.file_name}","${item.url}","${item.status}"`)
      ].join("\n");
      fs__namespace.writeFileSync(csvPath, csvContent, "utf8");
      sendLog(`Arquivo CSV criado: account_links.csv (${accountLinks.length} links de conta)`);
    } catch (error) {
      sendLog(`Erro ao criar arquivo CSV: ${error}`);
      console.error("Error creating account links CSV:", error);
    }
  }
  function extractDsidFromFilename(url) {
    try {
      const urlParts = url.split("/");
      const lastPart = urlParts[urlParts.length - 1];
      const filename = lastPart.split("?")[0];
      const accountMatch = filename.match(/(\d+)-ACCOUNT-(\d+)\.zip\.gpg$/);
      if (accountMatch) {
        const accountId = accountMatch[1];
        const dsid = accountMatch[2];
        sendLog(`DSID extraído corretamente do filename: ${dsid} (ID da conta: ${accountId})`);
        sendLog(`Arquivo: ${filename}`);
        return dsid;
      }
      sendLog(`Falha ao extrair DSID do filename: ${filename}`);
      return null;
    } catch (error) {
      console.error("Error extracting DSID from filename:", error);
      return null;
    }
  }
  async function createDownloadCSV(cloudId) {
    const links = await db.all(`
      SELECT
        'Account Information' as category,
        file_name,
        url as file_link,
        'GPG_ZIP_PWD' as file_type
      FROM work_queue
      WHERE cloud_id = ? AND status = 'pending'
      ORDER BY id
    `, [cloudId]);
    if (links.length === 0) {
      throw new Error("Nenhum link encontrado para download");
    }
    const csvContent = [
      "Category,FileName,URL,EncryptionType",
      ...links.map((link) => `"${link.category}","${link.file_name}","${link.file_link}","${link.file_type}"`)
    ].join("\n");
    const tempDir = path__namespace.join(__dirname, "../../temp");
    if (!fs__namespace.existsSync(tempDir)) {
      fs__namespace.mkdirSync(tempDir, { recursive: true });
    }
    const csvPath = path__namespace.join(tempDir, `cloud_${cloudId}_links.csv`);
    fs__namespace.writeFileSync(csvPath, csvContent);
    return csvPath;
  }
  electron.ipcMain.handle("cloud-start-download", async (_, cloudId) => {
    try {
      if (!db) throw new Error("Database not connected");
      if (activeDownloads.has(cloudId)) {
        return { success: false, message: "Download já está em andamento" };
      }
      const cloud = await db.get("SELECT * FROM clouds WHERE id = ?", [cloudId]);
      if (!cloud) throw new Error("Cloud not found");
      const settings = await db.get("SELECT bash_path FROM settings LIMIT 1");
      if (!settings?.bash_path) {
        return {
          success: false,
          message: "Caminho do Git Bash não configurado. Vá em Configurações e defina o caminho para o git-bash.exe"
        };
      }
      await db.run("UPDATE clouds SET status = ? WHERE id = ?", ["downloading", cloudId]);
      const csvPath = await createDownloadCSV(cloudId);
      const outputDir = cloud.output_dir || path__namespace.join(__dirname, "../../downloads", `cloud_${cloudId}`);
      if (!fs__namespace.existsSync(outputDir)) {
        fs__namespace.mkdirSync(outputDir, { recursive: true });
      }
      const bashScriptPath = path__namespace.join(__dirname, "../../_down.bash");
      console.log(`Starting download for cloud ${cloudId}: ${cloud.name}`);
      console.log(`CSV: ${csvPath}`);
      console.log(`Output: ${outputDir}`);
      console.log(`Password: ${cloud.password}`);
      const downloadProcess = child_process.spawn(settings.bash_path, [
        bashScriptPath,
        cloud.password,
        csvPath,
        outputDir
      ], {
        stdio: ["pipe", "pipe", "pipe"],
        shell: false
      });
      activeDownloads.set(cloudId, downloadProcess);
      downloadProcess.stdout?.on("data", (data) => {
        const output = data.toString();
        console.log(`Download ${cloudId} stdout:`, output);
        if (mainWindow) {
          mainWindow.webContents.send("download-progress", {
            cloudId,
            type: "stdout",
            data: output
          });
        }
      });
      downloadProcess.stderr?.on("data", (data) => {
        const output = data.toString();
        console.log(`Download ${cloudId} stderr:`, output);
        if (mainWindow) {
          mainWindow.webContents.send("download-progress", {
            cloudId,
            type: "stderr",
            data: output
          });
        }
      });
      downloadProcess.on("close", async (code) => {
        console.log(`Download process ${cloudId} exited with code ${code}`);
        activeDownloads.delete(cloudId);
        const newStatus = code === 0 ? "completed" : "failed";
        await db.run("UPDATE clouds SET status = ? WHERE id = ?", [newStatus, cloudId]);
        try {
          fs__namespace.unlinkSync(csvPath);
        } catch (err) {
          console.warn("Failed to delete CSV file:", err);
        }
        if (mainWindow) {
          mainWindow.webContents.send("download-completed", {
            cloudId,
            success: code === 0,
            message: code === 0 ? "Download concluído com sucesso" : "Download falhou"
          });
        }
      });
      downloadProcess.on("error", async (error) => {
        console.error(`Download process ${cloudId} error:`, error);
        activeDownloads.delete(cloudId);
        await db.run("UPDATE clouds SET status = ? WHERE id = ?", ["failed", cloudId]);
        if (mainWindow) {
          mainWindow.webContents.send("download-completed", {
            cloudId,
            success: false,
            message: `Erro no processo de download: ${error.message}`
          });
        }
      });
      return { success: true, message: "Download iniciado com sucesso" };
    } catch (error) {
      console.error("Error starting cloud download:", error);
      try {
        await db.run("UPDATE clouds SET status = ? WHERE id = ?", ["failed", cloudId]);
      } catch (dbError) {
        console.error("Failed to update cloud status:", dbError);
      }
      throw error;
    }
  });
  electron.ipcMain.handle("cloud-process-with-iped", async (_, cloudId) => {
    try {
      if (!db) throw new Error("Database not connected");
      console.log(`Processing IPED request for cloud ${cloudId}`);
      await db.run("UPDATE clouds SET status = ? WHERE id = ?", ["processing", cloudId]);
      const cloud = await db.get("SELECT * FROM clouds WHERE id = ?", [cloudId]);
      if (!cloud) {
        console.error(`Cloud ${cloudId} not found`);
        return { success: false, message: "Nuvem não encontrada" };
      }
      console.log(`Starting IPED processing for cloud ${cloudId}: ${cloud.name}`);
      return { success: true, message: "Processamento com IPED iniciado com sucesso" };
    } catch (error) {
      console.error("Error starting IPED processing:", error);
      return { success: false, message: `Erro ao processar com IPED: ${error.message}` };
    }
  });
  electron.ipcMain.handle("cloud-resolve-failed-downloads", async (_, cloudId) => {
    try {
      if (!db) throw new Error("Database not connected");
      console.log(`Resolving failed downloads for cloud ${cloudId}`);
      const cloud = await db.get("SELECT * FROM clouds WHERE id = ?", [cloudId]);
      if (!cloud) {
        console.error(`Cloud ${cloudId} not found`);
        return { success: false, message: "Nuvem não encontrada" };
      }
      const failedDownloads = await db.all(`
        SELECT * FROM work_queue
        WHERE cloud_id = ? AND status = 'failed'
      `, [cloudId]);
      console.log(`Found ${failedDownloads.length} failed downloads for cloud ${cloudId}`);
      if (failedDownloads.length === 0) {
        return { success: true, message: "Nenhum download falho encontrado" };
      }
      return { success: true, message: `${failedDownloads.length} downloads falhos encontrados e processados` };
    } catch (error) {
      console.error("Error resolving failed downloads:", error);
      return { success: false, message: `Erro ao resolver downloads falhos: ${error.message}` };
    }
  });
  electron.ipcMain.handle("cloud-set-target-name", async (_, cloudId, targetName, dsId) => {
    try {
      if (!db) throw new Error("Database not connected");
      console.log(`Setting target name for cloud ${cloudId}, dsId: ${dsId}, name: ${targetName}`);
      let account;
      if (dsId) {
        account = await db.get("SELECT ds_id, email FROM accounts WHERE cloud_id = ? AND ds_id = ?", [cloudId, dsId]);
      } else {
        account = await db.get("SELECT ds_id, email FROM accounts WHERE cloud_id = ? LIMIT 1", [cloudId]);
      }
      if (!account) {
        console.error(`No account found for cloud ${cloudId} and dsId ${dsId}`);
        return { success: false, message: "Conta não encontrada" };
      }
      console.log(`Found account: ${account.email} (${account.ds_id})`);
      const existingTarget = await db.get("SELECT id FROM targets WHERE ds_id = ?", [account.ds_id]);
      if (existingTarget) {
        await db.run("UPDATE targets SET name = ? WHERE ds_id = ?", [targetName, account.ds_id]);
        console.log(`Updated existing target for ${account.ds_id}`);
      } else {
        await db.run("INSERT INTO targets (ds_id, email, name) VALUES (?, ?, ?)", [account.ds_id, account.email, targetName]);
        console.log(`Created new target for ${account.ds_id}`);
      }
      return { success: true, message: "Nome do alvo definido com sucesso" };
    } catch (error) {
      console.error("Error setting target name:", error);
      return { success: false, message: `Erro ao definir nome do alvo: ${error.message}` };
    }
  });
  electron.ipcMain.handle("targets-get-all", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const targets = await db.all(`
        SELECT
          t.*,
          a.cloud_id,
          c.name as cloud_name
        FROM targets t
        LEFT JOIN accounts a ON t.ds_id = a.ds_id AND a.id = (
          SELECT MIN(a2.id) FROM accounts a2 WHERE a2.ds_id = t.ds_id
        )
        LEFT JOIN clouds c ON a.cloud_id = c.id
        ORDER BY t.name ASC
      `);
      return targets;
    } catch (error) {
      console.error("Error fetching targets:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("targets-create-or-update", async (_, targetData) => {
    try {
      if (!db) throw new Error("Database not connected");
      const { ds_id, email, name } = targetData;
      const existingTarget = await db.get("SELECT id FROM targets WHERE ds_id = ?", [ds_id]);
      if (existingTarget) {
        await db.run("UPDATE targets SET email = ?, name = ? WHERE ds_id = ?", [email, name, ds_id]);
        return { success: true, message: "Alvo atualizado com sucesso" };
      } else {
        await db.run("INSERT INTO targets (ds_id, email, name) VALUES (?, ?, ?)", [ds_id, email, name]);
        return { success: true, message: "Alvo criado com sucesso" };
      }
    } catch (error) {
      console.error("Error creating/updating target:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("targets-delete", async (_, dsId) => {
    try {
      if (!db) throw new Error("Database not connected");
      await db.run("DELETE FROM targets WHERE ds_id = ?", [dsId]);
      return { success: true, message: "Alvo removido com sucesso" };
    } catch (error) {
      console.error("Error deleting target:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("cloud-cancel-download", async (_, cloudId) => {
    try {
      const downloadProcess = activeDownloads.get(cloudId);
      if (downloadProcess) {
        downloadProcess.kill("SIGTERM");
        activeDownloads.delete(cloudId);
        await db.run("UPDATE clouds SET status = ? WHERE id = ?", ["cancelled", cloudId]);
        return { success: true, message: "Download cancelado" };
      } else {
        return { success: false, message: "Nenhum download ativo encontrado" };
      }
    } catch (error) {
      console.error("Error cancelling download:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("cloud-get-download-status", async (_, cloudId) => {
    try {
      const isActive = activeDownloads.has(cloudId);
      const cloud = await db.get("SELECT status FROM clouds WHERE id = ?", [cloudId]);
      return {
        isActive,
        status: cloud?.status || "unknown"
      };
    } catch (error) {
      console.error("Error getting download status:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("ipc-settings-get", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const settings = await db.get("SELECT * FROM settings LIMIT 1");
      return settings || {};
    } catch (error) {
      console.error("Error getting settings:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("database-get-stats", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const [cloudsCount, accountsCount, linksCount, targetsCount, queueCount] = await Promise.all([
        db.get("SELECT COUNT(*) as count FROM clouds"),
        db.get("SELECT COUNT(*) as count FROM accounts"),
        db.get("SELECT COUNT(*) as count FROM work_queue"),
        db.get("SELECT COUNT(*) as count FROM targets"),
        db.get('SELECT COUNT(*) as count FROM work_queue WHERE status = "pending"')
      ]);
      return {
        clouds: cloudsCount?.count || 0,
        accounts: accountsCount?.count || 0,
        links: linksCount?.count || 0,
        targets: targetsCount?.count || 0,
        workQueue: queueCount?.count || 0
      };
    } catch (error) {
      console.error("Error getting database stats:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("database-get-recent-activity", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const activities = [];
      try {
        const loggedActivities = await db.all(`
          SELECT action, table_name, timestamp, details
          FROM activity_log
          ORDER BY timestamp DESC
          LIMIT 10
        `);
        activities.push(...loggedActivities);
      } catch (error) {
      }
      try {
        const recentClouds = await db.all(`
          SELECT 'INSERT' as action, 'clouds' as table_name,
                 COALESCE(created_at, datetime('now')) as timestamp,
                 'Nova nuvem "' || name || '" criada' as details
          FROM clouds
          ORDER BY id DESC
          LIMIT 5
        `);
        activities.push(...recentClouds);
      } catch (error) {
        try {
          const recentClouds = await db.all(`
            SELECT 'INSERT' as action, 'clouds' as table_name,
                   datetime('now', '-' || (id * 10) || ' minutes') as timestamp,
                   'Nova nuvem "' || name || '" criada' as details
            FROM clouds
            ORDER BY id DESC
            LIMIT 5
          `);
          activities.push(...recentClouds);
        } catch (e) {
          console.log("Could not get clouds data:", e.message);
        }
      }
      try {
        const recentAccounts = await db.all(`
          SELECT 'INSERT' as action, 'accounts' as table_name,
                 COALESCE(created_at, datetime('now')) as timestamp,
                 'Nova conta Apple adicionada (' || email || ')' as details
          FROM accounts
          ORDER BY id DESC
          LIMIT 5
        `);
        activities.push(...recentAccounts);
      } catch (error) {
        try {
          const recentAccounts = await db.all(`
            SELECT 'INSERT' as action, 'accounts' as table_name,
                   datetime('now', '-' || (id * 15) || ' minutes') as timestamp,
                   'Nova conta Apple adicionada (' || email || ')' as details
            FROM accounts
            ORDER BY id DESC
            LIMIT 5
          `);
          activities.push(...recentAccounts);
        } catch (e) {
          console.log("Could not get accounts data:", e.message);
        }
      }
      try {
        const recentTargets = await db.all(`
          SELECT 'INSERT' as action, 'targets' as table_name,
                 COALESCE(created_at, datetime('now')) as timestamp,
                 'Novo alvo "' || name || '" adicionado' as details
          FROM targets
          ORDER BY id DESC
          LIMIT 3
        `);
        activities.push(...recentTargets);
      } catch (error) {
        try {
          const recentTargets = await db.all(`
            SELECT 'INSERT' as action, 'targets' as table_name,
                   datetime('now', '-' || (id * 20) || ' minutes') as timestamp,
                   'Novo alvo "' || name || '" adicionado' as details
            FROM targets
            ORDER BY id DESC
            LIMIT 3
          `);
          activities.push(...recentTargets);
        } catch (e) {
          console.log("Could not get targets data:", e.message);
        }
      }
      try {
        const recentQueue = await db.all(`
          SELECT 'UPDATE' as action, 'work_queue' as table_name,
                 COALESCE(updated_at, created_at, datetime('now')) as timestamp,
                 'Item de trabalho - Status: "' || COALESCE(status, 'pendente') || '"' as details
          FROM work_queue
          ORDER BY id DESC
          LIMIT 5
        `);
        activities.push(...recentQueue);
      } catch (error) {
        try {
          const recentQueue = await db.all(`
            SELECT 'UPDATE' as action, 'work_queue' as table_name,
                   datetime('now', '-' || (id * 5) || ' minutes') as timestamp,
                   'Item de trabalho - Status: "' || COALESCE(status, 'pendente') || '"' as details
            FROM work_queue
            ORDER BY id DESC
            LIMIT 5
          `);
          activities.push(...recentQueue);
        } catch (e) {
          console.log("Could not get work_queue data:", e.message);
        }
      }
      activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      if (activities.length === 0) {
        const sampleActivities = [
          {
            action: "SYSTEM",
            table_name: "database",
            timestamp: (/* @__PURE__ */ new Date()).toISOString(),
            details: "Sistema iniciado e banco de dados conectado"
          },
          {
            action: "INFO",
            table_name: "database",
            timestamp: new Date(Date.now() - 6e4).toISOString(),
            details: "Aguardando atividades do usuário"
          }
        ];
        activities.push(...sampleActivities);
      }
      return activities.slice(0, 15).map((activity, index) => ({
        id: index + 1,
        action: activity.action,
        table: activity.table_name,
        timestamp: new Date(activity.timestamp),
        details: activity.details
      }));
    } catch (error) {
      console.error("Error getting recent activity:", error);
      return [
        {
          id: 1,
          action: "SYSTEM",
          table: "database",
          timestamp: /* @__PURE__ */ new Date(),
          details: "Sistema iniciado com sucesso"
        },
        {
          id: 2,
          action: "INFO",
          table: "database",
          timestamp: new Date(Date.now() - 3e4),
          details: "Banco de dados conectado e pronto para uso"
        }
      ];
    }
  });
  const logActivity = async (action, table, details) => {
    try {
      if (!db) return;
      await db.run(`
        CREATE TABLE IF NOT EXISTS activity_log (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          action TEXT NOT NULL,
          table_name TEXT NOT NULL,
          details TEXT NOT NULL,
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      await db.run(`
        INSERT INTO activity_log (action, table_name, details)
        VALUES (?, ?, ?)
      `, [action, table, details]);
      await db.run(`
        DELETE FROM activity_log
        WHERE id NOT IN (
          SELECT id FROM activity_log
          ORDER BY timestamp DESC
          LIMIT 100
        )
      `);
    } catch (error) {
      console.error("Error logging activity:", error);
    }
  };
  electron.ipcMain.handle("database-update-stats", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      await db.run("ANALYZE");
      await logActivity("SYSTEM", "database", "Estatísticas do banco atualizadas");
      return { success: true, message: "Estatísticas atualizadas com sucesso" };
    } catch (error) {
      console.error("Error updating stats:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("database-clean-old-data", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const result = await db.run(`
        DELETE FROM work_queue
        WHERE status = 'completed'
        AND updated_at < datetime('now', '-30 days')
      `);
      await db.run("VACUUM");
      await logActivity("SYSTEM", "database", `Limpeza realizada: ${result.changes || 0} registros removidos`);
      return {
        success: true,
        message: `${result.changes || 0} registros antigos removidos. Espaço em disco otimizado.`
      };
    } catch (error) {
      console.error("Error cleaning old data:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("database-export-data", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const { dialog: dialog2 } = require("electron");
      const fs2 = require("fs");
      const result = await dialog2.showSaveDialog(mainWindow, {
        title: "Exportar Dados do Banco",
        defaultPath: `stratocumulus_backup_${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}.sql`,
        filters: [
          { name: "SQL Files", extensions: ["sql"] },
          { name: "All Files", extensions: ["*"] }
        ]
      });
      if (result.canceled) {
        return { success: false, message: "Exportação cancelada" };
      }
      const tables = await db.all(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `);
      let sqlContent = "-- Nimbus Database Export\n";
      sqlContent += `-- Generated on ${(/* @__PURE__ */ new Date()).toISOString()}

`;
      for (const table of tables) {
        const schema = await db.get(`
          SELECT sql FROM sqlite_master
          WHERE type='table' AND name='${table.name}'
        `);
        sqlContent += `-- Table: ${table.name}
`;
        sqlContent += `${schema.sql};

`;
        const rows = await db.all(`SELECT * FROM ${table.name}`);
        if (rows.length > 0) {
          const columns = Object.keys(rows[0]);
          sqlContent += `INSERT INTO ${table.name} (${columns.join(", ")}) VALUES
`;
          const values = rows.map((row) => {
            const vals = columns.map((col) => {
              const val = row[col];
              if (val === null) return "NULL";
              if (typeof val === "string") return `'${val.replace(/'/g, "''")}'`;
              return val;
            });
            return `(${vals.join(", ")})`;
          });
          sqlContent += values.join(",\n") + ";\n\n";
        }
      }
      fs2.writeFileSync(result.filePath, sqlContent);
      await logActivity("EXPORT", "database", `Dados exportados para: ${result.filePath}`);
      return {
        success: true,
        message: `Dados exportados com sucesso para: ${result.filePath}`
      };
    } catch (error) {
      console.error("Error exporting data:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("database-import-data", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const { dialog: dialog2 } = require("electron");
      const fs2 = require("fs");
      const result = await dialog2.showOpenDialog(mainWindow, {
        title: "Importar Dados do Banco",
        filters: [
          { name: "SQL Files", extensions: ["sql"] },
          { name: "All Files", extensions: ["*"] }
        ],
        properties: ["openFile"]
      });
      if (result.canceled || !result.filePaths.length) {
        return { success: false, message: "Importação cancelada" };
      }
      const sqlContent = fs2.readFileSync(result.filePaths[0], "utf8");
      await db.exec(sqlContent);
      await logActivity("IMPORT", "database", `Dados importados de: ${result.filePaths[0]}`);
      return {
        success: true,
        message: `Dados importados com sucesso de: ${result.filePaths[0]}`
      };
    } catch (error) {
      console.error("Error importing data:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("database-maintenance", async () => {
    try {
      if (!db) throw new Error("Database not connected");
      const integrityResult = await db.get("PRAGMA integrity_check");
      if (integrityResult.integrity_check !== "ok") {
        throw new Error("Falha na verificação de integridade do banco de dados");
      }
      await db.run("PRAGMA optimize");
      await db.run("VACUUM");
      await logActivity("SYSTEM", "database", "Manutenção completa realizada: integridade verificada e banco otimizado");
      return {
        success: true,
        message: "Manutenção do banco concluída com sucesso. Integridade verificada e banco otimizado."
      };
    } catch (error) {
      console.error("Error during maintenance:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("ipc-settings-update", async (_, newSettings) => {
    try {
      if (!db) throw new Error("Database not connected");
      const existingSettings = await db.get("SELECT id FROM settings LIMIT 1");
      if (existingSettings) {
        const updateFields = Object.keys(newSettings).map((key) => `${key} = ?`).join(", ");
        const updateValues = Object.values(newSettings);
        await db.run(`UPDATE settings SET ${updateFields} WHERE id = ?`, [...updateValues, existingSettings.id]);
      } else {
        const fields = Object.keys(newSettings).join(", ");
        const placeholders = Object.keys(newSettings).map(() => "?").join(", ");
        const values = Object.values(newSettings);
        await db.run(`INSERT INTO settings (${fields}) VALUES (${placeholders})`, values);
      }
      return { success: true };
    } catch (error) {
      console.error("Error updating settings:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("select-directory", async () => {
    const result = await electron.dialog.showOpenDialog({
      properties: ["openDirectory"]
    });
    return result.filePaths[0];
  });
  electron.ipcMain.handle("select-file", async (_, filters) => {
    const result = await electron.dialog.showOpenDialog({
      properties: ["openFile"],
      filters
    });
    return result.filePaths[0];
  });
  createWindow();
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
