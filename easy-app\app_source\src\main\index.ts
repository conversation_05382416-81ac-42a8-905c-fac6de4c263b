import { app, shell, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import { join } from 'node:path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import { open } from 'sqlite'
import sqlite3 from 'sqlite3'
import { spawn, ChildProcess } from 'child_process'
import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'
import AdmZ<PERSON> from 'adm-zip'
import * as XLSX from 'xlsx'

const icon = join(__dirname, '../../resources/icon.png')

// Database configuration
let db: any = null

// Download process management
const activeDownloads = new Map<number, ChildProcess>()
let mainWindow: BrowserWindow | null = null

async function initDatabase() {
  try {
    // Get the app's user data directory
    const { app } = require('electron')
    const userDataPath = app.getPath('userData')

    // In development, use the helper.db from the project root
    // In production, copy it to userData if it doesn't exist
    let dbPath: string

    if (is.dev) {
      // Development: use the database from the project root
      dbPath = join(__dirname, '../../helper.db')
    } else {
      // Production: use database in userData directory
      dbPath = join(userDataPath, 'helper.db')

      // Check if database exists in userData, if not copy from resources
      const fs = require('fs')
      if (!fs.existsSync(dbPath)) {
        // Try to find the database in the app resources
        const resourceDbPath = join(__dirname, '../../resources/helper.db')
        if (fs.existsSync(resourceDbPath)) {
          fs.copyFileSync(resourceDbPath, dbPath)
          console.log('Database copied to userData directory from app resources')
        } else {
          console.warn('Database not found in app resources, creating new one')
        }
      }
    }

    console.log('Database path:', dbPath)

    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    })
    console.log('Database connected successfully')
  } catch (error) {
    console.error('Failed to connect to database:', error)
  }
}

function createWindow(): void {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1080,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.stratocumulus.app')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC test
  ipcMain.handle('ping', () => 'pong')

  // Initialize database
  await initDatabase()

  // Initialize settings table and default values
  setTimeout(async () => {
    try {
      if (db) {
        // Create activity_log table
        await db.run(`
          CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            action TEXT NOT NULL,
            table_name TEXT NOT NULL,
            details TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `)

        // Create settings table if it doesn't exist
        await db.run(`
          CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bash_path TEXT,
            iped_path TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `)

        // Check if settings exist, if not create default entry
        const existingSettings = await db.get('SELECT id FROM settings LIMIT 1')
        if (!existingSettings) {
          await db.run(`
            INSERT INTO settings (bash_path, iped_path)
            VALUES (?, ?)
          `, [null, null])
          console.log('Default settings created')
        }

        await db.run(`
          INSERT INTO activity_log (action, table_name, details)
          VALUES (?, ?, ?)
        `, ['SYSTEM', 'application', 'Nimbus App iniciado com sucesso'])
      }
    } catch (error) {
      console.error('Error during startup initialization:', error)
    }
  }, 1000)

  // Database IPC handlers
  ipcMain.handle('clouds-get-all', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      const clouds = await db.all(`
        SELECT
          c.*,
          (SELECT COUNT(*) FROM accounts WHERE cloud_id = c.id) as accountsCount,
          (SELECT COUNT(*) FROM work_queue WHERE cloud_id = c.id) as linksCount,
          (SELECT t.name FROM targets t
           JOIN accounts a ON t.ds_id = a.ds_id
           WHERE a.cloud_id = c.id LIMIT 1) as target_name
        FROM clouds c
        ORDER BY c.created_at DESC
      `)

      return clouds.map(cloud => ({
        ...cloud,
        createdAt: new Date(cloud.created_at),
        updatedAt: new Date(cloud.updated_at || cloud.created_at)
      }))
    } catch (error) {
      console.error('Error fetching clouds:', error)
      throw error
    }
  })

  ipcMain.handle('clouds-create', async (_, cloudData) => {
    try {
      if (!db) throw new Error('Database not connected')

      sendLog('Iniciando criação da nuvem...')
      sendLog(`Nome da nuvem: ${cloudData.name}`)
      sendLog(`Arquivo CSV: ${cloudData.csvFile}`)
      sendLog(`Pasta de destino: ${cloudData.outputDir}`)

      sendLog('Criando registro da nuvem no banco de dados...')
      const result = await db.run(`
        INSERT INTO clouds (name, password, csv_links, output_dir, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
      `, [cloudData.name, cloudData.password, cloudData.csvFile, cloudData.outputDir])

      const cloudId = result.lastID
      sendLog(`Nuvem criada com ID: ${cloudId}`)

      // Process CSV file to extract accounts and links
      if (cloudData.csvFile && fs.existsSync(cloudData.csvFile)) {
        sendLog('Processando arquivo CSV...')
        await processCsvFile(cloudId, cloudData.csvFile, cloudData.outputDir, cloudData.password)
        sendLog('Processamento do arquivo CSV concluído!')
      } else {
        sendLog('Nenhum arquivo CSV válido fornecido')
      }

      sendLog('Criação da nuvem finalizada com sucesso!')
      return { id: cloudId, ...cloudData }
    } catch (error) {
      sendLog(`Erro ao criar nuvem: ${error}`)
      console.error('Error creating cloud:', error)
      throw error
    }
  })

  ipcMain.handle('clouds-update', async (_, id, cloudData) => {
    try {
      if (!db) throw new Error('Database not connected')

      // Get current cloud data to check if CSV file changed
      const currentCloud = await db.get('SELECT csv_links FROM clouds WHERE id = ?', [id])

      await db.run(`
        UPDATE clouds
        SET name = ?, password = ?, csv_links = ?, output_dir = ?
        WHERE id = ?
      `, [cloudData.name, cloudData.password, cloudData.csvFile, cloudData.outputDir, id])

      // If CSV file changed, reprocess it
      if (currentCloud && currentCloud.csv_links !== cloudData.csvFile) {
        if (cloudData.csvFile && fs.existsSync(cloudData.csvFile)) {
          // Clear existing accounts and links for this cloud
          await db.run('DELETE FROM accounts WHERE cloud_id = ?', [id])
          await db.run('DELETE FROM work_queue WHERE cloud_id = ?', [id])

          // Process new CSV file
          await processCsvFile(id, cloudData.csvFile, cloudData.outputDir, cloudData.password)
        }
      }

      return { id, ...cloudData }
    } catch (error) {
      console.error('Error updating cloud:', error)
      throw error
    }
  })

  ipcMain.handle('clouds-delete', async (_, id) => {
    try {
      if (!db) throw new Error('Database not connected')

      // Delete related data first
      await db.run('DELETE FROM accounts WHERE cloud_id = ?', [id])
      await db.run('DELETE FROM work_queue WHERE cloud_id = ?', [id])
      await db.run('DELETE FROM clouds WHERE id = ?', [id])

      return { success: true }
    } catch (error) {
      console.error('Error deleting cloud:', error)
      throw error
    }
  })

  // Reprocess CSV file for existing cloud
  ipcMain.handle('clouds-reprocess-csv', async (_, cloudId: number) => {
    try {
      if (!db) throw new Error('Database not connected')

      const cloud = await db.get('SELECT csv_links FROM clouds WHERE id = ?', [cloudId])
      if (!cloud) {
        return { success: false, message: 'Nuvem não encontrada' }
      }

      if (!cloud.csv_links || !fs.existsSync(cloud.csv_links)) {
        return { success: false, message: 'Arquivo CSV não encontrado' }
      }

      // Clear existing accounts and links for this cloud
      await db.run('DELETE FROM accounts WHERE cloud_id = ?', [cloudId])
      await db.run('DELETE FROM work_queue WHERE cloud_id = ?', [cloudId])

      // Process CSV file
      await processCsvFile(cloudId, cloud.csv_links, cloud.output_dir, cloud.password)

      return { success: true, message: 'CSV reprocessado com sucesso' }
    } catch (error) {
      console.error('Error reprocessing CSV:', error)
      return { success: false, message: `Erro ao reprocessar CSV: ${error.message}` }
    }
  })

  // Get cloud details with accounts and links
  ipcMain.handle('clouds-get-details', async (_, cloudId) => {
    try {
      if (!db) throw new Error('Database not connected')

      // Get cloud info with target name
      const cloud = await db.get(`
        SELECT c.*,
               (SELECT t.name FROM targets t
                JOIN accounts a ON t.ds_id = a.ds_id
                WHERE a.cloud_id = c.id LIMIT 1) as target_name
        FROM clouds c
        WHERE c.id = ?
      `, [cloudId])
      if (!cloud) throw new Error('Cloud not found')

      // Get accounts for this cloud with target names
      const accounts = await db.all(`
        SELECT a.*, t.name as alvo
        FROM accounts a
        LEFT JOIN targets t ON a.ds_id = t.ds_id
        WHERE a.cloud_id = ?
        ORDER BY a.id DESC
      `, [cloudId])

      // Get links for this cloud (usando a tabela work_queue que tem os links)
      const links = await db.all(`
        SELECT * FROM work_queue
        WHERE cloud_id = ?
        ORDER BY id DESC
      `, [cloudId])

      return {
        cloud: {
          ...cloud,
          createdAt: cloud.created_at ? new Date(cloud.created_at) : new Date(),
          updatedAt: cloud.updated_at ? new Date(cloud.updated_at || cloud.created_at) : new Date()
        },
        accounts: accounts.map(account => ({
          ...account,
          createdAt: account.created_at ? new Date(account.created_at) : new Date(),
          updatedAt: account.updated_at ? new Date(account.updated_at) : new Date()
        })),
        links: links.map(link => ({
          ...link,
          createdAt: link.created_at ? new Date(link.created_at) : new Date(),
          updatedAt: link.updated_at ? new Date(link.updated_at) : new Date()
        }))
      }
    } catch (error) {
      console.error('Error fetching cloud details:', error)
      throw error
    }
  })

  // Debug: Get table schema
  ipcMain.handle('debug-get-schema', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      const tables = await db.all(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `)

      const schema = {}
      for (const table of tables) {
        const columns = await db.all(`PRAGMA table_info(${table.name})`)
        schema[table.name] = columns
      }

      return schema
    } catch (error) {
      console.error('Error getting schema:', error)
      throw error
    }
  })

  // Helper function to send log to frontend
  function sendLog(message: string): void {
    const timestamp = new Date().toLocaleTimeString('pt-BR')
    const logMessage = `[${timestamp}] ${message}`
    console.log(logMessage)

    // Send log to all renderer processes
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('csv-processing-log', logMessage)
    })
  }

  // Helper function to process CSV file and extract accounts/links data
  async function processCsvFile(cloudId: number, csvFilePath: string, outputDir?: string, password?: string): Promise<void> {
    try {
      sendLog(`Iniciando processamento do arquivo CSV`)
      sendLog(`Arquivo: ${csvFilePath}`)

      const csvContent = fs.readFileSync(csvFilePath, 'utf-8')
      const lines = csvContent.split('\n').filter(line => line.trim())

      if (lines.length === 0) {
        sendLog('Arquivo CSV está vazio')
        return
      }

      // Parse header to understand CSV structure
      const header = lines[0].toLowerCase()
      sendLog(`Cabeçalho CSV: ${header}`)
      sendLog(`Total de linhas para processar: ${lines.length - 1}`)

      let accountsProcessed = 0
      let linksProcessed = 0

      // Use provided outputDir or get from cloud details
      let finalOutputDir = outputDir
      if (!finalOutputDir) {
        const cloud = await db.get('SELECT output_dir FROM clouds WHERE id = ?', [cloudId])
        finalOutputDir = cloud?.output_dir
      }

      // Process each line (skip header)
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim()
        if (!line) continue

        try {
          sendLog(`Processando linha ${i}/${lines.length - 1}`)

          // Parse CSV line (handle quoted fields)
          const fields = parseCsvLine(line)

          // Try to identify if this line contains account data or link data
          const isAccount = isAccountData(fields, header)
          const isLink = isLinkData(fields, header)

          if (isAccount) {
            await processAccountData(cloudId, fields, header)
            accountsProcessed++

            // Extract email for log
            const emailField = fields.find(field => field.includes('@'))
            const email = emailField ? emailField.trim() : 'N/A'
            sendLog(`Conta adicionada: ${email} (${accountsProcessed} total)`)
          } else if (isLink) {
            await processLinkData(cloudId, fields, header, finalOutputDir, password)
            linksProcessed++
            sendLog(`Link adicionado (${linksProcessed} total)`)
          } else {
            sendLog(`Linha ${i} ignorada - formato não reconhecido`)
          }
        } catch (error) {
          sendLog(`Erro na linha ${i}: ${error}`)
        }
      }

      // Count accounts extracted from links
      const totalAccounts = await db.get('SELECT COUNT(*) as count FROM accounts WHERE cloud_id = ?', [cloudId])
      const accountsFromLinks = totalAccounts?.count || 0

      sendLog(`=== Processamento CSV concluído ===`)
      sendLog(`Contas extraídas dos links: ${accountsFromLinks}`)
      sendLog(`Contas processadas diretamente: ${accountsProcessed}`)
      sendLog(`Links processados: ${linksProcessed}`)
      sendLog(`Total de linhas processadas: ${lines.length - 1}`)

      // Create download files if output directory is specified
      if (finalOutputDir && linksProcessed > 0) {
        await createDownloadFiles(cloudId, finalOutputDir)
      }
    } catch (error) {
      sendLog(`Erro ao processar arquivo CSV: ${error}`)
      throw error
    }
  }

  // Helper function to parse CSV line with proper quote handling
  function parseCsvLine(line: string): string[] {
    const fields: string[] = []
    let current = ''
    let inQuotes = false
    let i = 0

    while (i < line.length) {
      const char = line[i]

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"'
          i += 2
        } else {
          // Toggle quote state
          inQuotes = !inQuotes
          i++
        }
      } else if (char === ',' && !inQuotes) {
        // Field separator
        fields.push(current.trim())
        current = ''
        i++
      } else {
        current += char
        i++
      }
    }

    // Add the last field
    fields.push(current.trim())

    return fields
  }

  // Helper function to identify if line contains account data
  function isAccountData(fields: string[], header: string): boolean {
    // Look for typical account fields: DSID, email, size, expiry
    const hasEmail = fields.some(field => field.includes('@'))
    const hasDsid = fields.some(field => /^\d{10,}$/.test(field)) // DSID is typically 10+ digits
    const hasSize = fields.some(field => /\d+(\.\d+)?\s*(GB|MB|KB)/i.test(field))

    return hasEmail && (hasDsid || hasSize)
  }

  // Helper function to identify if line contains link data
  function isLinkData(fields: string[], header: string): boolean {
    // Look for URL patterns
    return fields.some(field =>
      field.startsWith('http://') ||
      field.startsWith('https://') ||
      field.includes('icloud.com') ||
      field.includes('apple.com')
    )
  }

  // Helper function to process account data
  async function processAccountData(cloudId: number, fields: string[], header: string): Promise<void> {
    try {
      // Extract account information from fields
      let dsId = ''
      let email = ''
      let cloudSize = ''
      let expiryDate = ''
      let alvo = '' // Campo para o nome do alvo

      console.log(`Processing account data - Fields: ${JSON.stringify(fields)}`)
      console.log(`Header: ${header}`)

      // Find email field
      const emailField = fields.find(field => field.includes('@'))
      if (emailField) email = emailField.trim()

      // Find DSID (typically 10+ digit number)
      const dsidField = fields.find(field => /^\d{10,}$/.test(field.trim()))
      if (dsidField) dsId = dsidField.trim()

      // Find size field (contains GB, MB, KB)
      const sizeField = fields.find(field => /\d+(\.\d+)?\s*(GB|MB|KB)/i.test(field))
      if (sizeField) cloudSize = sizeField.trim()

      // Find date field (various date formats)
      const dateField = fields.find(field =>
        /\d{2}[-\/]\d{2}[-\/]\d{4}/.test(field) ||
        /\d{4}[-\/]\d{2}[-\/]\d{2}/.test(field) ||
        /\d{2}[-\/]\d{2}[-\/]\d{2}/.test(field)
      )
      if (dateField) expiryDate = dateField.trim()

      // Try to find "Alvo" field - look for text fields that are not email, not numbers, not dates, not sizes
      const alvoField = fields.find(field => {
        const trimmed = field.trim()
        return trimmed.length > 0 &&
               !trimmed.includes('@') && // not email
               !/^\d+$/.test(trimmed) && // not pure number
               !/\d+(\.\d+)?\s*(GB|MB|KB)/i.test(trimmed) && // not size
               !/\d{2}[-\/]\d{2}[-\/]\d{4}/.test(trimmed) && // not date
               !/\d{4}[-\/]\d{2}[-\/]\d{2}/.test(trimmed) && // not date
               !/\d{2}[-\/]\d{2}[-\/]\d{2}/.test(trimmed) && // not date
               !trimmed.startsWith('http') && // not URL
               trimmed.length < 50 // reasonable name length
      })
      if (alvoField) alvo = alvoField.trim()

      console.log(`Extracted data - DSID: ${dsId}, Email: ${email}, Size: ${cloudSize}, Expiry: ${expiryDate}, Alvo: ${alvo}`)

      // If we have at least email or DSID, create the account
      if (email || dsId) {
        // Check if account already exists
        const existingAccount = await db.get(
          'SELECT id FROM accounts WHERE cloud_id = ? AND (ds_id = ? OR email = ?)',
          [cloudId, dsId, email]
        )

        if (!existingAccount) {
          await db.run(`
            INSERT INTO accounts (
              cloud_id, ds_id, email, cloud_size, expiry_date, alvo
            ) VALUES (?, ?, ?, ?, ?, ?)
          `, [cloudId, dsId, email, cloudSize, expiryDate, alvo])

          console.log(`Added account: ${email} (${dsId}) - Alvo: ${alvo}`)

          // If we have an alvo (target name) and dsId, create or update target
          if (alvo && dsId) {
            try {
              // Check if target already exists for this ds_id
              const existingTarget = await db.get('SELECT id FROM targets WHERE ds_id = ?', [dsId])

              if (existingTarget) {
                // Update existing target
                await db.run('UPDATE targets SET name = ?, email = ? WHERE ds_id = ?', [alvo, email, dsId])
                console.log(`Updated existing target for ${dsId}: ${alvo}`)
              } else {
                // Create new target
                await db.run('INSERT INTO targets (ds_id, email, name) VALUES (?, ?, ?)', [dsId, email, alvo])
                console.log(`Created new target for ${dsId}: ${alvo}`)
              }
            } catch (targetError) {
              console.error('Error creating/updating target:', targetError)
            }
          }
        } else {
          console.log(`Account already exists: ${email} (${dsId})`)
        }
      } else {
        console.warn(`Skipping line - no email or DSID found in fields: ${JSON.stringify(fields)}`)
      }
    } catch (error) {
      console.error('Error processing account data:', error)
    }
  }

  // Helper function to process link data
  async function processLinkData(cloudId: number, fields: string[], header: string, outputDir?: string, password?: string): Promise<void> {
    try {
      // Extract link information from fields
      let fileName = ''
      let fileLink = ''
      let fileType = ''
      let category = 'Account Information'

      // Find URL field
      const urlField = fields.find(field =>
        field.startsWith('http://') ||
        field.startsWith('https://') ||
        field.includes('icloud.com') ||
        field.includes('apple.com')
      )
      if (urlField) fileLink = urlField

      // Find filename (usually has extension or is descriptive)
      const filenameField = fields.find(field =>
        field.includes('.') ||
        (field.length > 5 && !field.includes('@') && !field.startsWith('http'))
      )
      if (filenameField) fileName = filenameField

      // Determine file type and category based on URL content
      if (fileLink.includes('backup') || fileName.includes('backup')) {
        fileType = 'backup'
        category = 'iCloud Backup'
      } else if (fileLink.includes('photo') || fileLink.includes('cloudphotolibrary')) {
        fileType = 'photos'
        category = 'iCloud Photos'
      } else if (fileLink.includes('mail')) {
        fileType = 'mail'
        category = 'iCloud Mail'
      } else if (fileLink.includes('contacts')) {
        fileType = 'contacts'
        category = 'iCloud Contacts'
      } else if (fileLink.includes('calendars')) {
        fileType = 'calendar'
        category = 'iCloud Calendar'
      } else if (fileLink.includes('notes')) {
        fileType = 'notes'
        category = 'iCloud Notes'
      } else if (fileLink.includes('reminders')) {
        fileType = 'reminders'
        category = 'iCloud Reminders'
      } else if (fileLink.includes('iclouddrive')) {
        fileType = 'drive'
        category = 'iCloud Drive'
      } else if (fileLink.includes('log')) {
        fileType = 'logs'
        category = 'Logs'
      } else if (fileLink.includes('account')) {
        fileType = 'account'
        category = 'Account Information'
      } else {
        fileType = 'data'
        category = 'Account Information'
      }

      // Extract better filename from URL if not provided
      if (!fileName && fileLink) {
        const urlParts = fileLink.split('/')
        const lastPart = urlParts[urlParts.length - 1]
        if (lastPart.includes('.')) {
          fileName = lastPart.split('?')[0] // Remove query parameters
        } else {
          fileName = `${category.replace(/\s+/g, '_')}_${Date.now()}.zip.gpg`
        }
      }

      // If this is an account link, extract account information (limited processing)
      if (fileLink.includes('apple-account')) {
        sendLog(`Detectado link de conta: ${fileLink}`)
        if (password) {
          // Check how many accounts we've already processed for this cloud
          const processedCount = await db.get(
            'SELECT COUNT(*) as count FROM accounts WHERE cloud_id = ?',
            [cloudId]
          )

          // Only process first few account links to extract real information
          const maxAccountsToProcess = 5 // Limit to 5 accounts for real extraction

          if (processedCount.count < maxAccountsToProcess) {
            sendLog(`Processando conta ${processedCount.count + 1}/${maxAccountsToProcess}...`)
            await extractAccountFromLink(cloudId, fileLink, password)
          } else {
            sendLog(`Limite de processamento atingido (${maxAccountsToProcess} contas). Extraindo apenas DSID do filename...`)
            // Just extract DSID from filename without downloading
            const dsId = extractDsidFromFilename(fileLink)
            if (dsId) {
              const existingAccount = await db.get(
                'SELECT id FROM accounts WHERE cloud_id = ? AND ds_id = ?',
                [cloudId, dsId]
              )

              if (!existingAccount) {
                await db.run(`
                  INSERT INTO accounts (
                    cloud_id, ds_id, email, cloud_size, expiry_date, alvo
                  ) VALUES (?, ?, ?, ?, ?, ?)
                `, [cloudId, dsId, `dsid_${dsId}@icloud.com`, '', '', `Account_${dsId}`])

                sendLog(`Conta adicionada (apenas DSID): ${dsId}`)
              }
            }
          }
        } else {
          sendLog(`Senha não fornecida para descriptografar conta`)
        }
      }

      // If we have a valid link, add it to work_queue
      if (fileLink) {
        // Check if link already exists
        const existingLink = await db.get(
          'SELECT id FROM work_queue WHERE cloud_id = ? AND url = ?',
          [cloudId, fileLink]
        )

        if (!existingLink) {
          await db.run(`
            INSERT INTO work_queue (
              cloud_id, file_name, url, status
            ) VALUES (?, ?, ?, 'pending')
          `, [cloudId, fileName, fileLink])

          console.log(`Added link: ${category} -> ${fileLink}`)
        }
      }
    } catch (error) {
      console.error('Error processing link data:', error)
    }
  }

  // Helper function to check if GPG is available
  async function checkGpgAvailability(): Promise<boolean> {
    return new Promise((resolve) => {
      const gpgProcess = spawn('gpg', ['--version'], {
        stdio: ['pipe', 'pipe', 'pipe']
      })

      gpgProcess.on('close', (code) => {
        resolve(code === 0)
      })

      gpgProcess.on('error', () => {
        resolve(false)
      })
    })
  }

  // Helper function to download, decrypt and extract real account name
  async function downloadAndExtractAccountInfo(accountLink: string, dsId: string, password: string): Promise<{ email: string, accountName: string, realDsid?: string }> {
    try {
      sendLog(`Baixando arquivo de conta para DSID ${dsId}...`)

      // Check if GPG is available
      const gpgAvailable = await checkGpgAvailability()
      if (!gpgAvailable) {
        sendLog(`GPG não está instalado ou não está disponível no PATH`)
        throw new Error('GPG not available')
      }
      sendLog(`GPG está disponível`)

      // Create temporary directory for this DSID
      const tempDir = path.join(os.tmpdir(), 'nimbus_accounts', dsId)
      await fs.promises.mkdir(tempDir, { recursive: true })

      // Extract filename from URL
      const urlParts = accountLink.split('/')
      const lastPart = urlParts[urlParts.length - 1]
      const encryptedFilename = lastPart.split('?')[0] // Remove query parameters
      const encryptedFilePath = path.join(tempDir, encryptedFilename)

      // Download the encrypted file
      sendLog(`Baixando: ${encryptedFilename}`)
      const response = await fetch(accountLink)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const buffer = await response.arrayBuffer()
      await fs.promises.writeFile(encryptedFilePath, Buffer.from(buffer))
      sendLog(`Arquivo baixado: ${encryptedFilename}`)

      // Decrypt the file using GPG
      const decryptedFilename = encryptedFilename.replace('.gpg', '')
      const decryptedFilePath = path.join(tempDir, decryptedFilename)

      sendLog(`Descriptografando arquivo com senha da nuvem...`)
      await decryptFile(encryptedFilePath, decryptedFilePath, password)

      // Remove encrypted file
      await fs.promises.unlink(encryptedFilePath)
      sendLog(`Arquivo encriptado removido`)

      // Extract ZIP file
      sendLog(`Extraindo arquivo ZIP...`)
      const extractDir = path.join(tempDir, 'extracted')
      await extractZipFile(decryptedFilePath, extractDir)

      // Remove ZIP file
      await fs.promises.unlink(decryptedFilePath)
      sendLog(`Arquivo ZIP removido`)

      // Find account name in extracted folders and Excel files
      const accountInfo = await findAccountNameInExtractedFiles(extractDir, dsId)

      // Clean up temporary directory
      await fs.promises.rmdir(tempDir, { recursive: true })
      sendLog(`Diretório temporário limpo`)

      if (accountInfo.realDsid) {
        sendLog(`Conta encontrada: ${accountInfo.accountName} (${accountInfo.email}) - DSID real: ${accountInfo.realDsid}`)
      } else {
        sendLog(`Conta encontrada: ${accountInfo.accountName} (${accountInfo.email})`)
      }

      return accountInfo

    } catch (error) {
      sendLog(`Erro ao processar conta DSID ${dsId}: ${error}`)
      // Return fallback values
      return {
        email: `dsid_${dsId}@icloud.com`,
        accountName: `Account_${dsId}`
      }
    }
  }

  // Helper function to decrypt GPG file
  async function decryptFile(encryptedPath: string, decryptedPath: string, password: string): Promise<void> {
    return new Promise((resolve, reject) => {
      sendLog(`Iniciando descriptografia GPG...`)
      sendLog(`Arquivo origem: ${encryptedPath}`)
      sendLog(`Arquivo destino: ${decryptedPath}`)
      sendLog(`Senha fornecida: ${password ? '[SENHA FORNECIDA]' : '[SENHA VAZIA]'}`)

      const gpgProcess = spawn('gpg', [
        '--batch',
        '--yes',
        '--pinentry-mode', 'loopback',
        '--passphrase', password,
        '--decrypt',
        '--output', decryptedPath,
        encryptedPath
      ], {
        stdio: ['pipe', 'pipe', 'pipe']
      })

      let stderr = ''
      let stdout = ''

      gpgProcess.stdout?.on('data', (data) => {
        stdout += data.toString()
      })

      gpgProcess.stderr?.on('data', (data) => {
        stderr += data.toString()
        sendLog(`GPG stderr: ${data.toString()}`)
      })

      gpgProcess.on('close', (code) => {
        sendLog(`GPG processo finalizado com código: ${code}`)
        if (stdout) sendLog(`GPG stdout: ${stdout}`)
        if (stderr) sendLog(`GPG stderr completo: ${stderr}`)

        if (code === 0) {
          sendLog(`Descriptografia GPG bem-sucedida!`)
          resolve()
        } else {
          sendLog(`Descriptografia GPG falhou com código ${code}`)
          reject(new Error(`GPG decryption failed with code ${code}. stderr: ${stderr}`))
        }
      })

      gpgProcess.on('error', (error) => {
        sendLog(`Erro no processo GPG: ${error.message}`)
        reject(new Error(`GPG process error: ${error.message}`))
      })
    })
  }

  // Helper function to extract ZIP file
  async function extractZipFile(zipPath: string, extractDir: string): Promise<void> {
    try {
      const zip = new AdmZip(zipPath)
      zip.extractAllTo(extractDir, true)
    } catch (error) {
      throw new Error(`ZIP extraction failed: ${error.message}`)
    }
  }

  // Helper function to read account details from Excel file
  async function readAccountDetailsFromExcel(filePath: string): Promise<{ email: string, dsid: string } | null> {
    try {
      sendLog(`Lendo planilha Excel: ${path.basename(filePath)}`)

      // Read the Excel file
      const workbook = XLSX.readFile(filePath)
      const sheetName = workbook.SheetNames[0] // Get first sheet
      const worksheet = workbook.Sheets[sheetName]

      // Convert to JSON with headers
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      sendLog(`Planilha contém ${data.length} linhas`)

      // Look for specific patterns based on the AccountDetails.xlsx structure
      let email = ''
      let dsid = ''

      for (let i = 0; i < data.length; i++) {
        const row = data[i] as any[]
        if (!row || row.length === 0) continue

        // Convert row to string for easier searching
        const rowStr = row.join(' ').toLowerCase()

        // Look for the Apple ID row (row 8 in the example: <EMAIL>)
        if (rowStr.includes('apple id') || (i >= 6 && i <= 10)) {
          for (const cell of row) {
            const cellStr = String(cell || '').trim()
            if (cellStr.includes('@') && cellStr.includes('.') && !cellStr.toLowerCase().includes('apple')) {
              email = cellStr
              sendLog(`Email encontrado na linha ${i + 1}: ${email}`)
              break
            }
          }
        }

        // Look for the DS ID row (row 8 in the example: ***********)
        if (rowStr.includes('ds id') || rowStr.includes('dsid') || (i >= 6 && i <= 10)) {
          for (const cell of row) {
            const cellStr = String(cell || '').trim()
            if (/^\d{10,}$/.test(cellStr)) {
              dsid = cellStr
              sendLog(`DSID encontrado na linha ${i + 1}: ${dsid}`)
              break
            }
          }
        }

        // Also check individual cells for email and DSID patterns
        for (const cell of row) {
          const cellStr = String(cell || '').trim()

          // Check if it's an email (and not a header)
          if (!email && cellStr.includes('@') && cellStr.includes('.') && !cellStr.toLowerCase().includes('apple') && !cellStr.toLowerCase().includes('id')) {
            email = cellStr
            sendLog(`Email encontrado: ${email}`)
          }

          // Check if it's a DSID (typically 10+ digits)
          if (!dsid && /^\d{10,}$/.test(cellStr)) {
            dsid = cellStr
            sendLog(`DSID encontrado: ${dsid}`)
          }
        }

        // If we found both, we can stop searching
        if (email && dsid) {
          break
        }
      }

      // If we found both email and DSID, return them
      if (email && dsid) {
        sendLog(`Informações extraídas da planilha - Email: ${email}, DSID: ${dsid}`)
        return { email, dsid }
      }

      sendLog(`Não foi possível extrair informações completas da planilha (Email: ${email || 'não encontrado'}, DSID: ${dsid || 'não encontrado'})`)
      return null

    } catch (error) {
      sendLog(`Erro ao ler planilha Excel: ${error}`)
      return null
    }
  }

  // Helper function to find account name in extracted files
  async function findAccountNameInExtractedFiles(extractDir: string, dsId: string): Promise<{ email: string, accountName: string, realDsid?: string }> {
    try {
      sendLog(`Procurando informações de conta em: ${extractDir}`)

      // First, look for Excel files with account details
      const accountInfo = await findAccountDetailsInDirectory(extractDir)
      if (accountInfo) {
        return {
          email: accountInfo.email,
          accountName: accountInfo.email.split('@')[0],
          realDsid: accountInfo.dsid // Return the real DSID from the Excel file
        }
      }

      // Fallback: Look for account-related files or folders
      const files = await fs.promises.readdir(extractDir, { withFileTypes: true })

      // Look for folders that might contain account info
      for (const file of files) {
        if (file.isDirectory()) {
          const folderPath = path.join(extractDir, file.name)

          // Check if folder name looks like an email or account name
          // Pattern: <EMAIL>-1601884
          if (file.name.includes('@')) {
            const parts = file.name.split('-')
            const email = parts[0] // <EMAIL>
            const accountId = parts[1] || dsId // 1601884 (ID da conta)

            return {
              email: email,
              accountName: `${email}-${accountId}` // Nome completo da pasta
            }
          }

          // Look inside folders for account info files
          try {
            const subFiles = await fs.promises.readdir(folderPath)
            for (const subFile of subFiles) {
              if (subFile.toLowerCase().includes('account') || subFile.toLowerCase().includes('info')) {
                const accountInfo = await extractAccountInfoFromFile(path.join(folderPath, subFile))
                if (accountInfo) {
                  return accountInfo
                }
              }
            }
          } catch (error) {
            // Continue searching
          }
        }
      }

      // If no specific account info found, use DSID-based fallback
      return {
        email: `dsid_${dsId}@icloud.com`,
        accountName: `Account_${dsId}`
      }

    } catch (error) {
      throw new Error(`Failed to find account name: ${error.message}`)
    }
  }

  // Helper function to recursively find AccountDetails.xlsx files
  async function findAccountDetailsInDirectory(dirPath: string): Promise<{ email: string, dsid: string } | null> {
    try {
      const files = await fs.promises.readdir(dirPath, { withFileTypes: true })

      // First, look for Excel files in current directory
      for (const file of files) {
        if (file.isFile() && file.name.toLowerCase().includes('accountdetails') && file.name.toLowerCase().endsWith('.xlsx')) {
          const filePath = path.join(dirPath, file.name)
          const accountInfo = await readAccountDetailsFromExcel(filePath)
          if (accountInfo) {
            return accountInfo
          }
        }
      }

      // Then, recursively search in subdirectories
      for (const file of files) {
        if (file.isDirectory()) {
          const subDirPath = path.join(dirPath, file.name)
          const accountInfo = await findAccountDetailsInDirectory(subDirPath)
          if (accountInfo) {
            return accountInfo
          }
        }
      }

      return null
    } catch (error) {
      sendLog(`Erro ao procurar planilhas em ${dirPath}: ${error}`)
      return null
    }
  }

  // Helper function to extract account info from file
  async function extractAccountInfoFromFile(filePath: string): Promise<{ email: string, accountName: string } | null> {
    try {
      const content = await fs.promises.readFile(filePath, 'utf8')

      // Look for email pattern in file content
      const emailMatch = content.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)
      if (emailMatch) {
        const email = emailMatch[1]
        const accountName = email.split('@')[0]
        return { email, accountName }
      }

      return null
    } catch (error) {
      return null
    }
  }

  // Helper function to extract account information from account link
  async function extractAccountFromLink(cloudId: number, accountLink: string, password: string): Promise<void> {
    try {
      // Extract DSID from filename (initial approach)
      const dsId = extractDsidFromFilename(accountLink)

      if (!dsId) {
        sendLog(`Não foi possível extrair DSID do filename: ${accountLink}`)
        return
      }

      sendLog(`Processando conta do link: DSID inicial ${dsId}`)

      // Check if account already exists
      const existingAccount = await db.get(
        'SELECT id FROM accounts WHERE cloud_id = ? AND ds_id = ?',
        [cloudId, dsId]
      )

      if (!existingAccount) {
        // Download, decrypt and extract real account information
        const accountInfo = await downloadAndExtractAccountInfo(accountLink, dsId, password)

        // Use the real DSID from the Excel file if available
        const finalDsid = accountInfo.realDsid || dsId

        // Check if account with real DSID already exists
        if (accountInfo.realDsid && accountInfo.realDsid !== dsId) {
          const existingRealAccount = await db.get(
            'SELECT id FROM accounts WHERE cloud_id = ? AND ds_id = ?',
            [cloudId, accountInfo.realDsid]
          )

          if (existingRealAccount) {
            sendLog(`Conta com DSID real ${accountInfo.realDsid} já existe`)
            return
          }
        }

        // Create new account record with real information
        await db.run(`
          INSERT INTO accounts (
            cloud_id, ds_id, email, cloud_size, expiry_date, alvo
          ) VALUES (?, ?, ?, ?, ?, ?)
        `, [cloudId, finalDsid, accountInfo.email, '', '', accountInfo.accountName])

        if (accountInfo.realDsid && accountInfo.realDsid !== dsId) {
          sendLog(`Conta adicionada: ${accountInfo.email} (DSID real: ${finalDsid}, DSID do filename: ${dsId}, Nome: ${accountInfo.accountName})`)
        } else {
          sendLog(`Conta adicionada: ${accountInfo.email} (DSID: ${finalDsid}, Nome: ${accountInfo.accountName})`)
        }
        console.log(`Added account from link: ${accountInfo.email} (${finalDsid})`)
      } else {
        sendLog(`Conta já existe: DSID ${dsId}`)
      }
    } catch (error) {
      sendLog(`Erro ao extrair conta do link: ${error}`)
      console.error('Error extracting account from link:', error)
    }
  }

  // Helper function to create download files in output directory with organized folder structure
  async function createDownloadFiles(cloudId: number, outputDir: string): Promise<void> {
    try {
      sendLog(`Criando estrutura de pastas organizada na pasta: ${outputDir}`)

      // Ensure output directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
        sendLog(`Pasta criada: ${outputDir}`)
      }

      // Get all links for this cloud with categorization
      const links = await db.all(`
        SELECT file_name, url, status
        FROM work_queue
        WHERE cloud_id = ? AND status = 'pending'
        ORDER BY id
      `, [cloudId])

      if (links.length === 0) {
        sendLog('Nenhum link encontrado para criar arquivos')
        return
      }

      // Get all accounts for this cloud to organize by DSID
      const accounts = await db.all(`
        SELECT ds_id, email
        FROM accounts
        WHERE cloud_id = ?
        ORDER BY ds_id
      `, [cloudId])

      sendLog(`Organizando ${links.length} links para ${accounts.length} contas`)

      // Create organized folder structure
      await createOrganizedFolderStructure(cloudId, outputDir, links, accounts)

      // Create CSV file with download links (for compatibility)
      const csvContent = [
        'Categoria,Nome do Arquivo,Link,Tipo,Tamanho',
        ...links.map(link => {
          const category = categorizeLink(link.url)
          return `"${category}","${link.file_name}","${link.url}","",""`;
        })
      ].join('\n')

      const csvPath = path.join(outputDir, `cloud_${cloudId}_links.csv`)
      fs.writeFileSync(csvPath, csvContent)
      sendLog(`Arquivo CSV criado: ${csvPath}`)

      // Create a simple text file with just the URLs for easier processing
      const urlsContent = links.map(link => link.url).join('\n')
      const urlsPath = path.join(outputDir, `cloud_${cloudId}_urls.txt`)
      fs.writeFileSync(urlsPath, urlsContent)
      sendLog(`Arquivo de URLs criado: ${urlsPath}`)

      sendLog(`${links.length} links salvos em arquivos na pasta de destino`)
    } catch (error) {
      sendLog(`Erro ao criar arquivos de download: ${error}`)
      console.error('Error creating download files:', error)
    }
  }

  // Helper function to categorize links based on URL content
  function categorizeLink(url: string): string {
    if (url.includes('backup') || url.includes('apple-backup')) return 'iCloud Backup'
    if (url.includes('photo') || url.includes('cloudphotolibrary')) return 'iCloud Photos'
    if (url.includes('mail') && !url.includes('mailheader')) return 'iCloud Mail'
    if (url.includes('mailheader')) return 'iCloud Mail'
    if (url.includes('contacts')) return 'iCloud Contacts'
    if (url.includes('calendars')) return 'iCloud Calendar'
    if (url.includes('notes')) return 'iCloud Notes'
    if (url.includes('reminders')) return 'iCloud Reminders'
    if (url.includes('iclouddrive')) return 'iCloud Drive'
    if (url.includes('log')) return 'Logs'
    if (url.includes('account')) return 'Account Information'
    if (url.includes('bookmarks')) return 'Account Information'
    if (url.includes('fmf')) return 'Account Information'
    if (url.includes('maps')) return 'Account Information'
    if (url.includes('messagesinicloud')) return 'Account Information'
    if (url.includes('signinwithapple')) return 'Account Information'
    if (url.includes('safaribrowsinghistory')) return 'Account Information'
    return 'Account Information'
  }

  // Helper function to extract DSID from filename (correct approach)
  function extractDsidFromFilename(url: string): string | null {
    try {
      // Extract filename from URL
      const urlParts = url.split('/')
      const lastPart = urlParts[urlParts.length - 1]
      const filename = lastPart.split('?')[0] // Remove query parameters

      // Look for pattern: ID-ACCOUNT-DSID.zip.gpg
      // Example: 1450879-ACCOUNT-********.zip.gpg -> DSID = ********
      const accountMatch = filename.match(/(\d+)-ACCOUNT-(\d+)\.zip\.gpg$/)

      if (accountMatch) {
        const accountId = accountMatch[1]  // 1450879 (ID da conta)
        const dsid = accountMatch[2]       // ******** (DSID real)

        sendLog(`DSID extraído corretamente do filename: ${dsid} (ID da conta: ${accountId})`)
        sendLog(`Arquivo: ${filename}`)
        return dsid
      }

      sendLog(`Falha ao extrair DSID do filename: ${filename}`)
      return null
    } catch (error) {
      console.error('Error extracting DSID from filename:', error)
      return null
    }
  }

  // Helper function to create organized folder structure
  async function createOrganizedFolderStructure(
    cloudId: number,
    outputDir: string,
    links: any[],
    accounts: any[]
  ): Promise<void> {
    try {
      sendLog(`Criando estrutura de pastas organizada por DSID`)

      // Create main accounts folder
      const accountsDir = path.join(outputDir, 'accounts')
      if (!fs.existsSync(accountsDir)) {
        fs.mkdirSync(accountsDir, { recursive: true })
        sendLog(`Pasta criada: accounts/`)
      }

      // Group links by DSID
      const linksByDsid: { [dsid: string]: any[] } = {}

      for (const link of links) {
        const dsid = extractDsidFromFilename(link.url)
        if (dsid) {
          if (!linksByDsid[dsid]) {
            linksByDsid[dsid] = []
          }
          linksByDsid[dsid].push(link)
        } else {
          // If we can't extract DSID, put in a general folder
          if (!linksByDsid['unknown']) {
            linksByDsid['unknown'] = []
          }
          linksByDsid['unknown'].push(link)
        }
      }

      sendLog(`Links agrupados por ${Object.keys(linksByDsid).length} DSIDs diferentes`)

      // Create folder structure for each DSID
      for (const [dsid, dsidLinks] of Object.entries(linksByDsid)) {
        await createDsidFolderStructure(accountsDir, dsid, dsidLinks, accounts)
      }

      sendLog(`Estrutura de pastas criada com sucesso`)
    } catch (error) {
      sendLog(`Erro ao criar estrutura de pastas: ${error}`)
      console.error('Error creating organized folder structure:', error)
    }
  }

  // Helper function to create folder structure for a specific DSID
  async function createDsidFolderStructure(
    accountsDir: string,
    dsid: string,
    links: any[],
    accounts: any[]
  ): Promise<void> {
    try {
      // Find account info for this DSID
      const account = accounts.find(acc => acc.ds_id === dsid)
      const email = account ? account.email : `dsid_${dsid}@icloud.com`

      // Create DSID folder: accounts/3848/
      const dsidDir = path.join(accountsDir, dsid)
      if (!fs.existsSync(dsidDir)) {
        fs.mkdirSync(dsidDir, { recursive: true })
        sendLog(`Pasta criada: accounts/${dsid}/`)
      }

      // Create account info file
      const accountInfoPath = path.join(dsidDir, 'account_info.txt')
      const accountInfo = [
        `DSID: ${dsid}`,
        `Email: ${email}`,
        `Total de arquivos: ${links.length}`,
        `Data de criação: ${new Date().toLocaleString('pt-BR')}`,
        '',
        'Categorias de dados:',
        ...getUniqueCategories(links).map(cat => `- ${cat}`)
      ].join('\n')

      fs.writeFileSync(accountInfoPath, accountInfo)
      sendLog(`Arquivo de informações criado: accounts/${dsid}/account_info.txt`)

      // Group links by category for this DSID
      const linksByCategory: { [category: string]: any[] } = {}

      for (const link of links) {
        const category = categorizeLink(link.url)
        if (!linksByCategory[category]) {
          linksByCategory[category] = []
        }
        linksByCategory[category].push(link)
      }

      // Create category folders and files
      for (const [category, categoryLinks] of Object.entries(linksByCategory)) {
        await createCategoryFolder(dsidDir, dsid, category, categoryLinks)
      }

      // Create master download list for this DSID
      const masterListPath = path.join(dsidDir, `dsid_${dsid}_all_links.txt`)
      const masterListContent = links.map(link => link.url).join('\n')
      fs.writeFileSync(masterListPath, masterListContent)
      sendLog(`Lista master criada: accounts/${dsid}/dsid_${dsid}_all_links.txt`)

    } catch (error) {
      sendLog(`Erro ao criar estrutura para DSID ${dsid}: ${error}`)
      console.error(`Error creating folder structure for DSID ${dsid}:`, error)
    }
  }

  // Helper function to get unique categories from links
  function getUniqueCategories(links: any[]): string[] {
    const categories = new Set<string>()
    for (const link of links) {
      categories.add(categorizeLink(link.url))
    }
    return Array.from(categories).sort()
  }

  // Helper function to create category folder and files
  async function createCategoryFolder(
    dsidDir: string,
    dsid: string,
    category: string,
    links: any[]
  ): Promise<void> {
    try {
      // Sanitize category name for folder
      const folderName = category.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_')
      const categoryDir = path.join(dsidDir, folderName)

      if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true })
        sendLog(`Pasta criada: accounts/${dsid}/${folderName}/`)
      }

      // Create category info file
      const categoryInfoPath = path.join(categoryDir, 'category_info.txt')
      const categoryInfo = [
        `Categoria: ${category}`,
        `DSID: ${dsid}`,
        `Total de arquivos: ${links.length}`,
        `Data de criação: ${new Date().toLocaleString('pt-BR')}`,
        '',
        'Arquivos nesta categoria:',
        ...links.map((link, index) => `${index + 1}. ${link.file_name}`)
      ].join('\n')

      fs.writeFileSync(categoryInfoPath, categoryInfo)

      // Create download list for this category
      const categoryLinksPath = path.join(categoryDir, `${folderName}_links.txt`)
      const categoryLinksContent = links.map(link => link.url).join('\n')
      fs.writeFileSync(categoryLinksPath, categoryLinksContent)

      // Create CSV for this category
      const categoryCsvPath = path.join(categoryDir, `${folderName}_download.csv`)
      const categoryCsvContent = [
        'Nome do Arquivo,URL,Categoria',
        ...links.map(link => `"${link.file_name}","${link.url}","${category}"`)
      ].join('\n')
      fs.writeFileSync(categoryCsvPath, categoryCsvContent)

      sendLog(`Categoria ${category} organizada: ${links.length} arquivos`)

    } catch (error) {
      sendLog(`Erro ao criar pasta para categoria ${category}: ${error}`)
      console.error(`Error creating category folder ${category}:`, error)
    }
  }

  // Helper function to create CSV file for download
  async function createDownloadCSV(cloudId: number): Promise<string> {
    const links = await db.all(`
      SELECT
        'Account Information' as category,
        file_name,
        url as file_link,
        'GPG_ZIP_PWD' as file_type
      FROM work_queue
      WHERE cloud_id = ? AND status = 'pending'
      ORDER BY id
    `, [cloudId])

    if (links.length === 0) {
      throw new Error('Nenhum link encontrado para download')
    }

    const csvContent = [
      'Category,FileName,URL,EncryptionType',
      ...links.map(link => `"${link.category}","${link.file_name}","${link.file_link}","${link.file_type}"`)
    ].join('\n')

    const tempDir = path.join(__dirname, '../../temp')
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true })
    }

    const csvPath = path.join(tempDir, `cloud_${cloudId}_links.csv`)
    fs.writeFileSync(csvPath, csvContent)

    return csvPath
  }

  // Cloud download and processing APIs
  ipcMain.handle('cloud-start-download', async (_, cloudId: number) => {
    try {
      if (!db) throw new Error('Database not connected')

      // Check if download is already running
      if (activeDownloads.has(cloudId)) {
        return { success: false, message: 'Download já está em andamento' }
      }

      // Get cloud details
      const cloud = await db.get('SELECT * FROM clouds WHERE id = ?', [cloudId])
      if (!cloud) throw new Error('Cloud not found')

      // Get settings for bash path
      const settings = await db.get('SELECT bash_path FROM settings LIMIT 1')
      if (!settings?.bash_path) {
        return {
          success: false,
          message: 'Caminho do Git Bash não configurado. Vá em Configurações e defina o caminho para o git-bash.exe'
        }
      }

      // Update cloud status to downloading
      await db.run('UPDATE clouds SET status = ? WHERE id = ?', ['downloading', cloudId])

      // Create CSV file with download links
      const csvPath = await createDownloadCSV(cloudId)

      // Prepare output directory
      const outputDir = cloud.output_dir || path.join(__dirname, '../../downloads', `cloud_${cloudId}`)
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
      }

      // Get bash script path
      const bashScriptPath = path.join(__dirname, '../../_down.bash')

      console.log(`Starting download for cloud ${cloudId}: ${cloud.name}`)
      console.log(`CSV: ${csvPath}`)
      console.log(`Output: ${outputDir}`)
      console.log(`Password: ${cloud.password}`)

      // Start download process
      const downloadProcess = spawn(settings.bash_path, [
        bashScriptPath,
        cloud.password,
        csvPath,
        outputDir
      ], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: false
      })

      // Store the process
      activeDownloads.set(cloudId, downloadProcess)

      // Handle process output
      downloadProcess.stdout?.on('data', (data) => {
        const output = data.toString()
        console.log(`Download ${cloudId} stdout:`, output)

        // Send progress to renderer
        if (mainWindow) {
          mainWindow.webContents.send('download-progress', {
            cloudId,
            type: 'stdout',
            data: output
          })
        }
      })

      downloadProcess.stderr?.on('data', (data) => {
        const output = data.toString()
        console.log(`Download ${cloudId} stderr:`, output)

        if (mainWindow) {
          mainWindow.webContents.send('download-progress', {
            cloudId,
            type: 'stderr',
            data: output
          })
        }
      })

      downloadProcess.on('close', async (code) => {
        console.log(`Download process ${cloudId} exited with code ${code}`)

        // Remove from active downloads
        activeDownloads.delete(cloudId)

        // Update cloud status
        const newStatus = code === 0 ? 'completed' : 'failed'
        await db.run('UPDATE clouds SET status = ? WHERE id = ?', [newStatus, cloudId])

        // Clean up CSV file
        try {
          fs.unlinkSync(csvPath)
        } catch (err) {
          console.warn('Failed to delete CSV file:', err)
        }

        // Notify renderer
        if (mainWindow) {
          mainWindow.webContents.send('download-completed', {
            cloudId,
            success: code === 0,
            message: code === 0 ? 'Download concluído com sucesso' : 'Download falhou'
          })
        }
      })

      downloadProcess.on('error', async (error) => {
        console.error(`Download process ${cloudId} error:`, error)

        // Remove from active downloads
        activeDownloads.delete(cloudId)

        // Update cloud status
        await db.run('UPDATE clouds SET status = ? WHERE id = ?', ['failed', cloudId])

        // Notify renderer
        if (mainWindow) {
          mainWindow.webContents.send('download-completed', {
            cloudId,
            success: false,
            message: `Erro no processo de download: ${error.message}`
          })
        }
      })

      return { success: true, message: 'Download iniciado com sucesso' }
    } catch (error) {
      console.error('Error starting cloud download:', error)

      // Update status to failed if there was an error
      try {
        await db.run('UPDATE clouds SET status = ? WHERE id = ?', ['failed', cloudId])
      } catch (dbError) {
        console.error('Failed to update cloud status:', dbError)
      }

      throw error
    }
  })

  ipcMain.handle('cloud-process-with-iped', async (_, cloudId: number) => {
    try {
      if (!db) throw new Error('Database not connected')

      console.log(`Processing IPED request for cloud ${cloudId}`)

      // Update cloud status to processing
      await db.run('UPDATE clouds SET status = ? WHERE id = ?', ['processing', cloudId])

      const cloud = await db.get('SELECT * FROM clouds WHERE id = ?', [cloudId])
      if (!cloud) {
        console.error(`Cloud ${cloudId} not found`)
        return { success: false, message: 'Nuvem não encontrada' }
      }

      console.log(`Starting IPED processing for cloud ${cloudId}: ${cloud.name}`)

      // TODO: Implement actual IPED processing logic here
      // For now, just simulate the process

      return { success: true, message: 'Processamento com IPED iniciado com sucesso' }
    } catch (error) {
      console.error('Error starting IPED processing:', error)
      return { success: false, message: `Erro ao processar com IPED: ${error.message}` }
    }
  })

  ipcMain.handle('cloud-resolve-failed-downloads', async (_, cloudId: number) => {
    try {
      if (!db) throw new Error('Database not connected')

      console.log(`Resolving failed downloads for cloud ${cloudId}`)

      const cloud = await db.get('SELECT * FROM clouds WHERE id = ?', [cloudId])
      if (!cloud) {
        console.error(`Cloud ${cloudId} not found`)
        return { success: false, message: 'Nuvem não encontrada' }
      }

      // Get failed downloads from work_queue
      const failedDownloads = await db.all(`
        SELECT * FROM work_queue
        WHERE cloud_id = ? AND status = 'failed'
      `, [cloudId])

      console.log(`Found ${failedDownloads.length} failed downloads for cloud ${cloudId}`)

      // TODO: Implement actual retry logic here
      // For now, just report the count

      if (failedDownloads.length === 0) {
        return { success: true, message: 'Nenhum download falho encontrado' }
      }

      return { success: true, message: `${failedDownloads.length} downloads falhos encontrados e processados` }
    } catch (error) {
      console.error('Error resolving failed downloads:', error)
      return { success: false, message: `Erro ao resolver downloads falhos: ${error.message}` }
    }
  })

  ipcMain.handle('cloud-set-target-name', async (_, cloudId: number, targetName: string, dsId?: string) => {
    try {
      if (!db) throw new Error('Database not connected')

      console.log(`Setting target name for cloud ${cloudId}, dsId: ${dsId}, name: ${targetName}`)

      let account
      if (dsId) {
        // Get specific account by ds_id
        account = await db.get('SELECT ds_id, email FROM accounts WHERE cloud_id = ? AND ds_id = ?', [cloudId, dsId])
      } else {
        // Fallback: Get the first account from this cloud (backward compatibility)
        account = await db.get('SELECT ds_id, email FROM accounts WHERE cloud_id = ? LIMIT 1', [cloudId])
      }

      if (!account) {
        console.error(`No account found for cloud ${cloudId} and dsId ${dsId}`)
        return { success: false, message: 'Conta não encontrada' }
      }

      console.log(`Found account: ${account.email} (${account.ds_id})`)

      // Check if target already exists for this ds_id
      const existingTarget = await db.get('SELECT id FROM targets WHERE ds_id = ?', [account.ds_id])

      if (existingTarget) {
        // Update existing target
        await db.run('UPDATE targets SET name = ? WHERE ds_id = ?', [targetName, account.ds_id])
        console.log(`Updated existing target for ${account.ds_id}`)
      } else {
        // Create new target
        await db.run('INSERT INTO targets (ds_id, email, name) VALUES (?, ?, ?)', [account.ds_id, account.email, targetName])
        console.log(`Created new target for ${account.ds_id}`)
      }

      return { success: true, message: 'Nome do alvo definido com sucesso' }
    } catch (error) {
      console.error('Error setting target name:', error)
      return { success: false, message: `Erro ao definir nome do alvo: ${error.message}` }
    }
  })

  // Get all targets
  ipcMain.handle('targets-get-all', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      const targets = await db.all(`
        SELECT
          t.*,
          a.cloud_id,
          c.name as cloud_name
        FROM targets t
        LEFT JOIN accounts a ON t.ds_id = a.ds_id AND a.id = (
          SELECT MIN(a2.id) FROM accounts a2 WHERE a2.ds_id = t.ds_id
        )
        LEFT JOIN clouds c ON a.cloud_id = c.id
        ORDER BY t.name ASC
      `)

      return targets
    } catch (error) {
      console.error('Error fetching targets:', error)
      throw error
    }
  })

  // Create or update target
  ipcMain.handle('targets-create-or-update', async (_, targetData: { ds_id: string, email: string, name: string }) => {
    try {
      if (!db) throw new Error('Database not connected')

      const { ds_id, email, name } = targetData

      // Check if target already exists
      const existingTarget = await db.get('SELECT id FROM targets WHERE ds_id = ?', [ds_id])

      if (existingTarget) {
        // Update existing target
        await db.run('UPDATE targets SET email = ?, name = ? WHERE ds_id = ?', [email, name, ds_id])
        return { success: true, message: 'Alvo atualizado com sucesso' }
      } else {
        // Create new target
        await db.run('INSERT INTO targets (ds_id, email, name) VALUES (?, ?, ?)', [ds_id, email, name])
        return { success: true, message: 'Alvo criado com sucesso' }
      }
    } catch (error) {
      console.error('Error creating/updating target:', error)
      throw error
    }
  })

  // Delete target
  ipcMain.handle('targets-delete', async (_, dsId: string) => {
    try {
      if (!db) throw new Error('Database not connected')

      await db.run('DELETE FROM targets WHERE ds_id = ?', [dsId])
      return { success: true, message: 'Alvo removido com sucesso' }
    } catch (error) {
      console.error('Error deleting target:', error)
      throw error
    }
  })

  // Cancel download
  ipcMain.handle('cloud-cancel-download', async (_, cloudId: number) => {
    try {
      const downloadProcess = activeDownloads.get(cloudId)

      if (downloadProcess) {
        downloadProcess.kill('SIGTERM')
        activeDownloads.delete(cloudId)

        // Update cloud status
        await db.run('UPDATE clouds SET status = ? WHERE id = ?', ['cancelled', cloudId])

        return { success: true, message: 'Download cancelado' }
      } else {
        return { success: false, message: 'Nenhum download ativo encontrado' }
      }
    } catch (error) {
      console.error('Error cancelling download:', error)
      throw error
    }
  })

  // Get download status
  ipcMain.handle('cloud-get-download-status', async (_, cloudId: number) => {
    try {
      const isActive = activeDownloads.has(cloudId)
      const cloud = await db.get('SELECT status FROM clouds WHERE id = ?', [cloudId])

      return {
        isActive,
        status: cloud?.status || 'unknown'
      }
    } catch (error) {
      console.error('Error getting download status:', error)
      throw error
    }
  })

  // Settings APIs
  ipcMain.handle('ipc-settings-get', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      const settings = await db.get('SELECT * FROM settings LIMIT 1')
      return settings || {}
    } catch (error) {
      console.error('Error getting settings:', error)
      throw error
    }
  })

  // Database Statistics APIs
  ipcMain.handle('database-get-stats', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      const [cloudsCount, accountsCount, linksCount, targetsCount, queueCount] = await Promise.all([
        db.get('SELECT COUNT(*) as count FROM clouds'),
        db.get('SELECT COUNT(*) as count FROM accounts'),
        db.get('SELECT COUNT(*) as count FROM work_queue'),
        db.get('SELECT COUNT(*) as count FROM targets'),
        db.get('SELECT COUNT(*) as count FROM work_queue WHERE status = "pending"')
      ])

      return {
        clouds: cloudsCount?.count || 0,
        accounts: accountsCount?.count || 0,
        links: linksCount?.count || 0,
        targets: targetsCount?.count || 0,
        workQueue: queueCount?.count || 0
      }
    } catch (error) {
      console.error('Error getting database stats:', error)
      throw error
    }
  })

  // Database Recent Activity API
  ipcMain.handle('database-get-recent-activity', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      // Get recent activities from different tables
      const activities = []

      // Get from activity log first (if exists)
      try {
        const loggedActivities = await db.all(`
          SELECT action, table_name, timestamp, details
          FROM activity_log
          ORDER BY timestamp DESC
          LIMIT 10
        `)
        activities.push(...loggedActivities)
      } catch (error) {
        // Table might not exist yet, continue with other sources
      }

      // Recent clouds (check if columns exist first)
      try {
        const recentClouds = await db.all(`
          SELECT 'INSERT' as action, 'clouds' as table_name,
                 COALESCE(created_at, datetime('now')) as timestamp,
                 'Nova nuvem "' || name || '" criada' as details
          FROM clouds
          ORDER BY id DESC
          LIMIT 5
        `)
        activities.push(...recentClouds)
      } catch (error) {
        // Column might not exist, try with just id
        try {
          const recentClouds = await db.all(`
            SELECT 'INSERT' as action, 'clouds' as table_name,
                   datetime('now', '-' || (id * 10) || ' minutes') as timestamp,
                   'Nova nuvem "' || name || '" criada' as details
            FROM clouds
            ORDER BY id DESC
            LIMIT 5
          `)
          activities.push(...recentClouds)
        } catch (e) {
          console.log('Could not get clouds data:', e.message)
        }
      }

      // Recent accounts
      try {
        const recentAccounts = await db.all(`
          SELECT 'INSERT' as action, 'accounts' as table_name,
                 COALESCE(created_at, datetime('now')) as timestamp,
                 'Nova conta Apple adicionada (' || email || ')' as details
          FROM accounts
          ORDER BY id DESC
          LIMIT 5
        `)
        activities.push(...recentAccounts)
      } catch (error) {
        try {
          const recentAccounts = await db.all(`
            SELECT 'INSERT' as action, 'accounts' as table_name,
                   datetime('now', '-' || (id * 15) || ' minutes') as timestamp,
                   'Nova conta Apple adicionada (' || email || ')' as details
            FROM accounts
            ORDER BY id DESC
            LIMIT 5
          `)
          activities.push(...recentAccounts)
        } catch (e) {
          console.log('Could not get accounts data:', e.message)
        }
      }

      // Recent targets
      try {
        const recentTargets = await db.all(`
          SELECT 'INSERT' as action, 'targets' as table_name,
                 COALESCE(created_at, datetime('now')) as timestamp,
                 'Novo alvo "' || name || '" adicionado' as details
          FROM targets
          ORDER BY id DESC
          LIMIT 3
        `)
        activities.push(...recentTargets)
      } catch (error) {
        try {
          const recentTargets = await db.all(`
            SELECT 'INSERT' as action, 'targets' as table_name,
                   datetime('now', '-' || (id * 20) || ' minutes') as timestamp,
                   'Novo alvo "' || name || '" adicionado' as details
            FROM targets
            ORDER BY id DESC
            LIMIT 3
          `)
          activities.push(...recentTargets)
        } catch (e) {
          console.log('Could not get targets data:', e.message)
        }
      }

      // Recent work queue updates
      try {
        const recentQueue = await db.all(`
          SELECT 'UPDATE' as action, 'work_queue' as table_name,
                 COALESCE(updated_at, created_at, datetime('now')) as timestamp,
                 'Item de trabalho - Status: "' || COALESCE(status, 'pendente') || '"' as details
          FROM work_queue
          ORDER BY id DESC
          LIMIT 5
        `)
        activities.push(...recentQueue)
      } catch (error) {
        try {
          const recentQueue = await db.all(`
            SELECT 'UPDATE' as action, 'work_queue' as table_name,
                   datetime('now', '-' || (id * 5) || ' minutes') as timestamp,
                   'Item de trabalho - Status: "' || COALESCE(status, 'pendente') || '"' as details
            FROM work_queue
            ORDER BY id DESC
            LIMIT 5
          `)
          activities.push(...recentQueue)
        } catch (e) {
          console.log('Could not get work_queue data:', e.message)
        }
      }

      // Sort all activities by timestamp
      activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

      // If no activities found, create some sample activities based on existing data
      if (activities.length === 0) {
        const sampleActivities = [
          {
            action: 'SYSTEM',
            table_name: 'database',
            timestamp: new Date().toISOString(),
            details: 'Sistema iniciado e banco de dados conectado'
          },
          {
            action: 'INFO',
            table_name: 'database',
            timestamp: new Date(Date.now() - 60000).toISOString(),
            details: 'Aguardando atividades do usuário'
          }
        ]
        activities.push(...sampleActivities)
      }

      return activities.slice(0, 15).map((activity, index) => ({
        id: index + 1,
        action: activity.action,
        table: activity.table_name,
        timestamp: new Date(activity.timestamp),
        details: activity.details
      }))
    } catch (error) {
      console.error('Error getting recent activity:', error)
      // Return fallback activities in case of error
      return [
        {
          id: 1,
          action: 'SYSTEM',
          table: 'database',
          timestamp: new Date(),
          details: 'Sistema iniciado com sucesso'
        },
        {
          id: 2,
          action: 'INFO',
          table: 'database',
          timestamp: new Date(Date.now() - 30000),
          details: 'Banco de dados conectado e pronto para uso'
        }
      ]
    }
  })

  // Helper function to log activity
  const logActivity = async (action: string, table: string, details: string) => {
    try {
      if (!db) return

      // Create activity log table if it doesn't exist
      await db.run(`
        CREATE TABLE IF NOT EXISTS activity_log (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          action TEXT NOT NULL,
          table_name TEXT NOT NULL,
          details TEXT NOT NULL,
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // Insert activity
      await db.run(`
        INSERT INTO activity_log (action, table_name, details)
        VALUES (?, ?, ?)
      `, [action, table, details])

      // Keep only last 100 activities
      await db.run(`
        DELETE FROM activity_log
        WHERE id NOT IN (
          SELECT id FROM activity_log
          ORDER BY timestamp DESC
          LIMIT 100
        )
      `)
    } catch (error) {
      console.error('Error logging activity:', error)
    }
  }

  // Database Operations APIs
  ipcMain.handle('database-update-stats', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      // Run ANALYZE to update SQLite statistics
      await db.run('ANALYZE')

      // Log activity
      await logActivity('SYSTEM', 'database', 'Estatísticas do banco atualizadas')

      return { success: true, message: 'Estatísticas atualizadas com sucesso' }
    } catch (error) {
      console.error('Error updating stats:', error)
      throw error
    }
  })

  ipcMain.handle('database-clean-old-data', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      // Clean old completed work queue items (older than 30 days)
      const result = await db.run(`
        DELETE FROM work_queue
        WHERE status = 'completed'
        AND updated_at < datetime('now', '-30 days')
      `)

      // Run VACUUM to reclaim space
      await db.run('VACUUM')

      // Log activity
      await logActivity('SYSTEM', 'database', `Limpeza realizada: ${result.changes || 0} registros removidos`)

      return {
        success: true,
        message: `${result.changes || 0} registros antigos removidos. Espaço em disco otimizado.`
      }
    } catch (error) {
      console.error('Error cleaning old data:', error)
      throw error
    }
  })

  ipcMain.handle('database-export-data', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      const { dialog } = require('electron')
      const fs = require('fs')

      // Show save dialog
      const result = await dialog.showSaveDialog(mainWindow, {
        title: 'Exportar Dados do Banco',
        defaultPath: `stratocumulus_backup_${new Date().toISOString().split('T')[0]}.sql`,
        filters: [
          { name: 'SQL Files', extensions: ['sql'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      })

      if (result.canceled) {
        return { success: false, message: 'Exportação cancelada' }
      }

      // Get all table names
      const tables = await db.all(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `)

      let sqlContent = '-- Nimbus Database Export\n'
      sqlContent += `-- Generated on ${new Date().toISOString()}\n\n`

      // Export each table
      for (const table of tables) {
        // Get table schema
        const schema = await db.get(`
          SELECT sql FROM sqlite_master
          WHERE type='table' AND name='${table.name}'
        `)

        sqlContent += `-- Table: ${table.name}\n`
        sqlContent += `${schema.sql};\n\n`

        // Get table data
        const rows = await db.all(`SELECT * FROM ${table.name}`)

        if (rows.length > 0) {
          const columns = Object.keys(rows[0])
          sqlContent += `INSERT INTO ${table.name} (${columns.join(', ')}) VALUES\n`

          const values = rows.map(row => {
            const vals = columns.map(col => {
              const val = row[col]
              if (val === null) return 'NULL'
              if (typeof val === 'string') return `'${val.replace(/'/g, "''")}'`
              return val
            })
            return `(${vals.join(', ')})`
          })

          sqlContent += values.join(',\n') + ';\n\n'
        }
      }

      fs.writeFileSync(result.filePath, sqlContent)

      // Log activity
      await logActivity('EXPORT', 'database', `Dados exportados para: ${result.filePath}`)

      return {
        success: true,
        message: `Dados exportados com sucesso para: ${result.filePath}`
      }
    } catch (error) {
      console.error('Error exporting data:', error)
      throw error
    }
  })

  ipcMain.handle('database-import-data', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      const { dialog } = require('electron')
      const fs = require('fs')

      // Show open dialog
      const result = await dialog.showOpenDialog(mainWindow, {
        title: 'Importar Dados do Banco',
        filters: [
          { name: 'SQL Files', extensions: ['sql'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      })

      if (result.canceled || !result.filePaths.length) {
        return { success: false, message: 'Importação cancelada' }
      }

      const sqlContent = fs.readFileSync(result.filePaths[0], 'utf8')

      // Execute the SQL content
      await db.exec(sqlContent)

      // Log activity
      await logActivity('IMPORT', 'database', `Dados importados de: ${result.filePaths[0]}`)

      return {
        success: true,
        message: `Dados importados com sucesso de: ${result.filePaths[0]}`
      }
    } catch (error) {
      console.error('Error importing data:', error)
      throw error
    }
  })

  ipcMain.handle('database-maintenance', async () => {
    try {
      if (!db) throw new Error('Database not connected')

      // Run integrity check
      const integrityResult = await db.get('PRAGMA integrity_check')

      if (integrityResult.integrity_check !== 'ok') {
        throw new Error('Falha na verificação de integridade do banco de dados')
      }

      // Optimize database
      await db.run('PRAGMA optimize')
      await db.run('VACUUM')

      // Log activity
      await logActivity('SYSTEM', 'database', 'Manutenção completa realizada: integridade verificada e banco otimizado')

      return {
        success: true,
        message: 'Manutenção do banco concluída com sucesso. Integridade verificada e banco otimizado.'
      }
    } catch (error) {
      console.error('Error during maintenance:', error)
      throw error
    }
  })

  ipcMain.handle('ipc-settings-update', async (_, newSettings) => {
    try {
      if (!db) throw new Error('Database not connected')

      // Check if settings record exists
      const existingSettings = await db.get('SELECT id FROM settings LIMIT 1')

      if (existingSettings) {
        // Update existing settings
        const updateFields = Object.keys(newSettings).map(key => `${key} = ?`).join(', ')
        const updateValues = Object.values(newSettings)

        await db.run(`UPDATE settings SET ${updateFields} WHERE id = ?`, [...updateValues, existingSettings.id])
      } else {
        // Insert new settings
        const fields = Object.keys(newSettings).join(', ')
        const placeholders = Object.keys(newSettings).map(() => '?').join(', ')
        const values = Object.values(newSettings)

        await db.run(`INSERT INTO settings (${fields}) VALUES (${placeholders})`, values)
      }

      return { success: true }
    } catch (error) {
      console.error('Error updating settings:', error)
      throw error
    }
  })

  // Example IPC handlers
  ipcMain.handle('select-directory', async () => {
    const result = await dialog.showOpenDialog({
      properties: ['openDirectory']
    })
    return result.filePaths[0]
  })

  ipcMain.handle('select-file', async (_, filters) => {
    const result = await dialog.showOpenDialog({
      properties: ['openFile'],
      filters
    })
    return result.filePaths[0]
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app"s main process code.
// You can also put them in separate files and require them here.
